# Column Header Debugging Analysis

## Root Cause Identified

The issue is in the `getCustomizedFieldsColumns` function in `getApplicationModulesSystemRows.ts` (lines 202-242).

### The Problem

**Event Application Exports:**
- Include EventApplications (ApplicationKind.Event)
- `getCustomizedFieldsColumns()` returns customized field columns
- Result: **Extra columns for customized fields**

**Regular Application Exports:**
- Include StandardApplications, ConfiguratorApplications, etc. (NOT ApplicationKind.Event)
- `getCustomizedFieldsColumns()` returns empty array
- Result: **No customized field columns**

### Code Analysis

```javascript
const getCustomizedFieldsColumns = (items: PreparedSystemApplicationData[]) => {
    const eventApplications = items.filter(({ kind }) => kind === ApplicationKind.Event);

    if (!eventApplications.length) {
        return []; // ← Regular applications get NO customized columns
    }

    // ← Event applications get customized field columns
    return customizedFieldColumns;
};
```

### Debug Logging Added

1. **Format Settings Comparison** - Added to both export functions
2. **Customized Fields Analysis** - Added to getCustomizedFieldsColumns
3. **Final Column Headers** - Added to getApplicationModulesSystemRows

### Expected Debug Output

**Event Application Export:**
```
[EventApplicationExport] Format settings: { format: 'system', currencyCode: 'USD', ... }
[Excel] Customized fields analysis: { eventApplicationsCount: 5, hasCustomizedFields: true }
[Excel] Final column headers: { totalColumns: 25, headers: [..., 'Custom Field 1', 'Custom Field 2'] }
```

**Regular Application Export:**
```
[ApplicationExport] Format settings: { format: 'system', currencyCode: 'USD', ... }
[Excel] Customized fields analysis: { eventApplicationsCount: 0, hasCustomizedFields: false }
[Excel] Final column headers: { totalColumns: 23, headers: [...] } // No custom fields
```

## Next Steps

1. **Run both exports** to confirm the debug output matches our hypothesis
2. **Decide on solution approach:**
   - Option A: Remove customized fields from event exports (breaking change)
   - Option B: Add customized fields to regular exports (may not make sense)
   - Option C: Create separate column header logic for consistency

## Files Modified

- `src/server/queues/implementations/processEventApplicationExport.ts` - Added format settings debug logging
- `src/server/queues/implementations/processApplicationExport.ts` - Added format settings debug logging  
- `src/server/utils/excel/applications/system/getApplicationModulesSystemRows.ts` - Added customized fields and final headers debug logging
