import { SalesOfferFeatureKind, SalesOfferFeatureStatus } from '../documents';
import type { SalesOffer, VSASalesOffer } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '283_addVsaForSalesOffer',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const now = new Date();
        await db.collection<SalesOffer>('salesOffers').updateMany(
            {
                $or: [{ vsa: { $exists: false } }, { vsa: { $eq: null } }],
            },
            {
                $set: {
                    vsa: {
                        status: SalesOfferFeatureStatus.Updated,
                        lastUpdatedAt: now,
                        kind: SalesOfferFeatureKind.VSA,
                        documents: [],
                        isEnabled: true,
                    } as VSASalesOffer,
                },
            }
        );
    },
};
