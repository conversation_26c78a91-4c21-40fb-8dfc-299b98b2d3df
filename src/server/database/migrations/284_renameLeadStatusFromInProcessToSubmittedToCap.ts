import { LeadStatus } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '284_renameLeadStatusFromInProcessToSubmittedToCap',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        await db
            .collection('leads')
            .updateMany({ status: 'inProcess' }, { $set: { status: LeadStatus.SubmittedToCap } });
    },
};
