import { ModuleType } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '285_removeCustomerModuleFromSalesOfferModule',

    /**
     *
     * removing the customerModuleId field from SalesOfferModule documents
     * due to in sales offer journey, it should refers to
     * 2the launchpad module setting ( agreement module  & customer module)
     */
    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        await db
            .collection('modules')
            .updateMany({ _type: ModuleType.SalesOfferModule }, { $unset: { customerModuleId: 1 } });
    },
};
