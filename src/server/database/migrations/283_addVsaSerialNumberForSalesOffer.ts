import { getVsaSerialNumber } from '../../schema/resolvers/mutations/salesOffers/helpers';
import type { Company, SalesOffer, SalesOfferModule } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '283_addVsaSerialNumberForSalesOffer',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const salesOfferCollection = db.collection<SalesOffer>('salesOffers');
        const salesOffers = await salesOfferCollection.find({ vsaSerialNumber: { $exists: false } }).toArray();

        const operations = await Promise.all(
            salesOffers.map(async salesOffer => {
                const salesOfferModule = await db
                    .collection<SalesOfferModule>('modules')
                    .findOne({ _id: salesOffer.moduleId });
                const company = await db.collection<Company>('companies').findOne({
                    _id: salesOfferModule.companyId,
                });

                const vsaSerialNumber = await getVsaSerialNumber(company, salesOfferModule.vsaCounter);

                return {
                    updateOne: {
                        filter: { _id: salesOffer._id },
                        update: {
                            $set: {
                                vsaSerialNumber,
                            },
                        },
                    },
                };
            })
        );

        if (operations.length > 0) {
            await salesOfferCollection.bulkWrite(operations);
        }
    },
};
