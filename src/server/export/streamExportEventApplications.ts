import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { isArray, isString } from 'lodash/fp';
import { ObjectId } from 'mongodb';

import { ApplicationStage as FrontendStage } from '../../app/utilities/getApplicationFileName';
import type { RequestLocals } from '../core/express';
import { ApplicationKind, ApplicationStatus } from '../database';
import type { EventApplicationModule } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { ApplicationPolicyAction } from '../permissions';
import { mainQueue } from '../queues';
import { getSessionDataFromRequest } from '../schema/session';
import { ExcelExportFormat } from '../utils/excel/enums';
import { uniqueObjectIds } from '../utils/fp';
import { getBEApplicationStage } from './exportApplications';
import type { PeriodPayload } from './type';

// Debug logger utility
interface StreamLogger {
    debug: (message: string, meta?: Record<string, unknown>) => void;
    error: (message: string, meta?: Record<string, unknown>) => void;
}

const logger: StreamLogger = {
    debug: (message: string, meta?: Record<string, unknown>) => {
        if (process.env.NODE_ENV !== 'production') {
            // eslint-disable-next-line no-console
            console.debug(`[streamExportEventApplications] ${message}`, meta);
        }
    },
    error: (message: string, meta?: Record<string, unknown>) => {
        // eslint-disable-next-line no-console
        console.error(`[streamExportEventApplications] ${message}`, meta);
    },
};

// Convert string stage to frontend enum for getBEApplicationStage
const convertStringToFrontendStage = (stage: string): FrontendStage | null => {
    switch (stage) {
        case 'Financing':
            return FrontendStage.Financing;
        case 'Lead':
            return FrontendStage.Lead;
        case 'Reservation':
            return FrontendStage.Reservation;
        case 'Mobility':
            return FrontendStage.Mobility;
        case 'Appointment':
            return FrontendStage.Appointment;
        case 'Insurance':
            return FrontendStage.Insurance;
        case 'VisitAppointment':
            return FrontendStage.VisitAppointment;
        case 'TradeIn':
            return FrontendStage.TradeIn;
        default:
            return null;
    }
};

interface Param {
    eventId: string;
}

interface ExportRequestBody {
    dealerIds: string[];
    applicationIds: string[];
    stage: string;
    period?: PeriodPayload;
    format: ExcelExportFormat.system | ExcelExportFormat.reporting;
    languageId?: string;
    nonce?: string;
    filename?: string[];
    company?: { countryCode?: string; displayName?: string };
}

type QueryParam = Record<string, never>;

/**
 * Queue a background job to export event applications and send the result via email
 */
const streamExportEventApplications: RequestHandler<
    Param,
    unknown,
    ExportRequestBody,
    QueryParam,
    RequestLocals
> = async (req, res, next) => {
    try {
        logger.debug('Received export request', {
            params: req.params,
            body: req.body,
            headers: req.headers,
        });

        const { getPermissionController } = res.locals.context;
        const permissions = await getPermissionController();

        const { eventId: inputEventId } = req.params;

        if (!ObjectId.isValid(inputEventId)) {
            logger.debug('Invalid eventId', { inputEventId });
            res.status(400).send('Bad request');

            return;
        }

        const {
            dealerIds: queryDealerIds,
            applicationIds: applicationStringIds,
            stage: inputStage,
            period,
            format,
            languageId,
            filename,
        } = req.body;

        // Validate application ids
        if ((!isArray(applicationStringIds) || !applicationStringIds?.length) && !period) {
            logger.debug('Invalid applicationIds', { applicationStringIds });
            res.status(400).send('Bad request');

            return;
        }

        const applicationIds = uniqueObjectIds(
            (applicationStringIds ?? []).filter(id => isString(id) && ObjectId.isValid(id)).map(it => new ObjectId(it))
        );

        // If it's still empty, return 404
        if (applicationIds.length === 0 && !period) {
            logger.debug('No valid applicationIds after filtering');
            res.status(404).send('Not found');

            return;
        }

        const frontendStage = convertStringToFrontendStage(inputStage);
        if (!frontendStage) {
            logger.debug('Invalid stage', { inputStage });
            res.status(400).send('Bad request');

            return;
        }
        getBEApplicationStage(frontendStage); // Validate stage

        const { collections } = await getDatabaseContext();

        const eventId = new ObjectId(inputEventId);
        const event = await collections.events.findOne({ _id: eventId });
        if (!event) {
            logger.debug('Event not found', { eventId });
            res.status(404).send('Not found');

            return;
        }

        const userToken = req.headers.Authorization as string;
        const session = await getSessionDataFromRequest(req, userToken);
        const { userId } = session;

        // Get dealer IDs from request
        const dealerIdsInRequest = queryDealerIds?.map(dealerId => new ObjectId(dealerId)) ?? [];

        // Get application permissions
        const applicationPermission = await permissions.applications.getFilterQueryForAction(
            ApplicationPolicyAction.View
        );

        // Get event module
        const eventModule = (await collections.modules.findOne({
            _id: event.moduleId,
        })) as EventApplicationModule;

        if (!eventModule) {
            logger.debug('Event module not found', { eventModuleId: event.moduleId });
            res.status(404).send('Event module not found');

            return;
        }

        // Check if there are any applications to export
        const totalApplications = await collections.applications.countDocuments({
            $and: [
                applicationPermission,
                {
                    eventId,
                    isDraft: false,
                    status: { $ne: ApplicationStatus.Drafted },
                    dealerId: { $in: dealerIdsInRequest },
                    kind: ApplicationKind.Event,
                    ...(applicationIds.length > 0 ? { _id: { $in: applicationIds } } : {}),
                },
            ],
        });

        if (totalApplications === 0) {
            logger.debug('No applications found to export');
            res.status(204).end();

            return;
        }

        // Queue the job with validated data
        logger.debug('Queueing export job', {
            userId,
            eventId: inputEventId,
            applicationCount: applicationStringIds.length,
            dealerCount: queryDealerIds.length,
            stage: inputStage,
            format,
            hasLanguageId: !!languageId,
            hasFilename: !!filename,
            filename,
        });
        await mainQueue.add({
            type: 'processEventApplicationExport',
            userId,
            eventId: inputEventId,
            applicationIds: applicationStringIds,
            dealerIds: queryDealerIds,
            stage: inputStage,
            period,
            format,
            nonce: req.body.nonce,
            languageId,
            filename,
        });

        res.status(200).send('OK');
    } catch (error) {
        console.error('Error in streamExportEventApplications:', error);

        if (!res.headersSent) {
            res.status(500).send(
                `Error queuing export job: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
        } else {
            next(error);
        }
    }
};

export default streamExportEventApplications;
