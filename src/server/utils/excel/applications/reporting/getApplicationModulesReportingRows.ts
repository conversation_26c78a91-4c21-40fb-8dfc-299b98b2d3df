import { TFunction } from 'i18next';
import { capitalize, flatten, flow, isNil, map, uniq, uniqBy } from 'lodash/fp';
import { ApplicationStage, type Module, ModuleType } from '../../../../database';
import { getFormattedDateOnly, getTimeZoneOffset } from '../../../date';
import getSystemAppointmentSetting from '../shared/setting/appointment';
import { getCampaignValuesColumns } from '../shared/setting/campaign';
import { getCapColumns } from '../shared/setting/cap';
import {
    APPLICATION_EXCEL_MODULE_TYPES,
    type GetApplicationRowsSupportingData,
    type SystemKycSupportedApplicationModule,
    type SystemSupportedApplicationModule,
    type PreparedSystemApplicationData,
} from '../shared/types';
import getSystemKycSetting from './setting/kyc';
import getReportingMainSetting, { ReportingMainColumnSetting } from './setting/main';
import getReportingMainSettingId from './setting/mainId';
import getSystemVehicleSetting from './setting/vehicle';

const getMainSettingColumnId = (stage: ApplicationStage, moduleTypes?: ModuleType[]) => {
    const [mainColumnId, allMainColumnIds] = getReportingMainSettingId({
        id: `${capitalize(stage)} ID`,
    });

    const columns: ReportingMainColumnSetting[] = [];

    if (isNil(moduleTypes) || moduleTypes.some(moduleType => APPLICATION_EXCEL_MODULE_TYPES.includes(moduleType))) {
        columns.push(...allMainColumnIds);
    }

    return uniqBy('key', columns);
};

const getMainSettingColumns = (moduleTypes: ModuleType[], support: GetApplicationRowsSupportingData) => {
    const { stage, timeZone, t } = support;
    const [mainColumn, allMainColumns] = getReportingMainSetting(stage, {
        createdAt: timeZone ? `Date Created ${getTimeZoneOffset(t, timeZone)}` : 'Date Created',
        lastActivity: timeZone ? `Last Activity ${getTimeZoneOffset(t, timeZone)}` : 'Last Activity',
        status: 'Status',
        timeZone,
        t,
    });
    const columns: ReportingMainColumnSetting[] = [];

    if (moduleTypes.some(moduleType => APPLICATION_EXCEL_MODULE_TYPES.includes(moduleType))) {
        columns.push(...allMainColumns);
    }

    return uniqBy('key', columns);
};

export const getKYCColumns = async (modules: SystemKycSupportedApplicationModule[], t: TFunction) => {
    const columns = [];

    if (modules.some(module => APPLICATION_EXCEL_MODULE_TYPES.includes(module._type))) {
        const settings = (await Promise.all(modules.map(module => getSystemKycSetting(module, t)))).filter(Boolean);

        columns.push(
            ...settings.flatMap(([, allKycColumns]) =>
                modules.length > 1
                    ? allKycColumns.filter(column =>
                          settings.every(setting => setting.find(value => value[column.header]))
                      )
                    : allKycColumns
            )
        );
    }

    return uniqBy('key', columns);
};

const getVehicleColumns = (moduleTypes: ModuleType[], t: TFunction) => {
    const [vehicleColumn, allVehicleColumns] = getSystemVehicleSetting(t);

    const columns = [];

    if (moduleTypes.some(moduleType => APPLICATION_EXCEL_MODULE_TYPES.includes(moduleType))) {
        columns.push(...allVehicleColumns);
    }

    return uniqBy('key', columns);
};

const getEventDisplayNameColumn = (t: TFunction) => ({
    key: 'eventDisplayName',
    header: t('applicationExcel:main.leadCaptureFormName'),
    getCellValue: ({ eventDisplayName }) => eventDisplayName ?? '',
});

const getLastModifiedDateColumn = (t: TFunction) => ({
    key: 'lastModifiedDate',
    header: t('applicationExcel:main.lastModified'),
    getCellValue: ({ _versioning, support: { company } }, { t }) =>
        getFormattedDateOnly(t, _versioning.updatedAt, company?.timeZone),
});

const getRowSettingsForApplications = async (
    modules: SystemSupportedApplicationModule[],
    moduleTypes: ModuleType[],
    items: PreparedSystemApplicationData[],
    support: GetApplicationRowsSupportingData
) => {
    const { stage, t, timeZone } = support;

    const [, allAppointmentColumns] = getSystemAppointmentSetting({ t, timeZone });

    const kycColumns = await getKYCColumns(modules as SystemKycSupportedApplicationModule[], t);

    return flatten([
        // Application Ids
        ...getMainSettingColumnId(stage, moduleTypes),

        // C@P related values
        ...getCapColumns(t),

        // Appointment details
        allAppointmentColumns,

        // Application Details
        ...getMainSettingColumns(moduleTypes, support),

        // Vehicle Details
        ...getVehicleColumns(moduleTypes, t),

        // Campaign Values
        getCampaignValuesColumns(t, true),

        // Event Display Name
        getEventDisplayNameColumn(t),

        // Last Modified Date
        getLastModifiedDateColumn(t),

        ...kycColumns,
    ]).filter(Boolean);
};

const getApplicationModulesReportingRows = async (
    modules: SystemSupportedApplicationModule[],
    items: PreparedSystemApplicationData[],
    support: GetApplicationRowsSupportingData
) => {
    // ensure that the application modules together are on the same group

    // get all application module types
    const moduleTypes = flow([map((module: Module) => module._type), uniq])(modules);
    const settings = await getRowSettingsForApplications(modules, moduleTypes, items, support);

    const header = [[...settings.filter(Boolean).map(setting => setting.header)]];
    const body = items.map(item => settings.filter(Boolean).map(setting => setting.getCellValue(item, support)));

    return [...header, ...body];
};

export default getApplicationModulesReportingRows;
