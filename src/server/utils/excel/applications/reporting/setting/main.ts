import { TFunction } from 'i18next';
import { isNil, keyBy, uniq, upperFirst } from 'lodash/fp';
import { ApplicationStage, ApplicationStatus } from '../../../../../database';
import { getFormattedDate } from '../../../../date';
import { getAuthorText, getLastModifiedUser } from '../../../utils';
import type { SystemApplicationColumnSetting } from '../../shared/types';

export enum ReportingMainSettingKey {
    Id = 'id',
    Status = 'status',
    CreatedAt = 'createdAt',
    DealerName = 'dealerName',
    CreatorName = 'creatorName',
    AssigneeName = 'assigneeName',
    Module = 'module',
    SalespersonReferenceCode = 'salespersonRefCode',
    Reference = 'reference',
    LastActivity = 'lastActivity',
    AppointmentDate = 'appointmentDate',
}

export type ReportingMainColumnSetting = SystemApplicationColumnSetting & {
    key: ReportingMainSettingKey;
};

type GetReportingMainHeaderProps = {
    id?: string;
    createdAt: string;
    lastActivity: string;
    status: string;
    timeZone?: string;
    t: TFunction;
};

type ReportingMainKey = { [key in ReportingMainSettingKey]: ReportingMainColumnSetting };

const getReportingMainSetting = (stage: ApplicationStage, header: GetReportingMainHeaderProps) => {
    const { t } = header;
    const allSettings: ReportingMainColumnSetting[] = [
        {
            key: ReportingMainSettingKey.Status,
            header: header.status,
            getCellValue: ({ support: { appStage } }, { t }) => {
                const { status } = appStage.value;

                // Some cases, like "otpCompleted" saved in database,
                // but "translation" record have "OTPCompleted" as key
                if (status === ApplicationStatus.OTPCompleted) {
                    return t('applicationList:status.OTPCompleted') as string;
                }

                return status ? (t(`applicationList:status.${upperFirst(appStage.value.status)}`) as string) : '';
            },
        },
        {
            key: ReportingMainSettingKey.CreatedAt,
            header: header.createdAt,
            getCellValue: ({ _id, _versioning, support }, { t }) =>
                getFormattedDate(t, _versioning.createdAt, support.company?.timeZone),
        },
        {
            key: ReportingMainSettingKey.DealerName,
            header: t('applicationExcel:main.dealer'),
            getCellValue: ({ support: { dealer } }) => dealer?.displayName ?? '',
        },
        {
            key: ReportingMainSettingKey.CreatorName,
            header: t('applicationExcel:main.createdBy'),
            getCellValue: (preparedData, { t }) =>
                preparedData.support.creatorName
                    ? getAuthorText(t, preparedData._versioning.createdBy.kind, preparedData.support.creatorName)
                    : '',
        },
        {
            key: ReportingMainSettingKey.AssigneeName,
            header: t('applicationExcel:main.salesperson'),
            getCellValue: ({ support: { assignee }, ...data }) => assignee?.displayName ?? '',
        },
        {
            key: ReportingMainSettingKey.SalespersonReferenceCode,
            header: t('applicationExcel:main.salespersonRefCode'),
            getCellValue: ({ support: { assignee } }) => assignee?.alias ?? '',
        },
        {
            key: ReportingMainSettingKey.Module,
            header: t('applicationExcel:main.module'),
            getCellValue: ({ support: { module } }) => module.displayName,
        },
        {
            key: ReportingMainSettingKey.Reference,
            header: t('applicationExcel:main.otherApplicationReference'),
            getCellValue: ({ support, stages }, { t }) => {
                const filter = support.referenceApplications.flatMap(app => {
                    const list = stages.filter(listStage => listStage !== stage);

                    const reference = list
                        .map(listStage => {
                            if (!isNil(app?.[`${listStage}Stage`])) {
                                return app?.[`${listStage}Stage`]?.identifier;
                            }

                            return null;
                        })
                        .filter(Boolean);

                    return reference;
                });

                return filter.length ? uniq(filter).join(', ') : '';
            },
        },
        {
            key: ReportingMainSettingKey.LastActivity,
            header: header.lastActivity,
            getCellValue: ({ _versioning, support: { editorName, company } }, { t }) => {
                const formattedEditorName = getAuthorText(t, _versioning.updatedBy.kind, editorName);

                return getLastModifiedUser(t, _versioning.updatedAt, formattedEditorName, company?.timeZone);
            },
        },
    ];

    return [keyBy('key', allSettings) as ReportingMainKey, allSettings] as const;
};

export default getReportingMainSetting;
