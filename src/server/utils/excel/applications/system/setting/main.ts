import dayjs from 'dayjs';
import type { TFunction } from 'i18next';
import { isNil, keyBy, uniq, upperFirst } from 'lodash/fp';
import { ApplicationKind, ApplicationStage, ApplicationStatus, AuditTrailKind } from '../../../../../database';
import { getFormattedDate, getTimeZoneOffset } from '../../../../date';
import { getAuthorText, getLastModifiedUser } from '../../../utils';
import type { SystemApplicationColumnSetting } from '../../shared/types';

export enum SystemMainSettingKey {
    Id = 'id',
    Status = 'status',
    CreatedAt = 'createdAt',
    DealerName = 'dealerName',
    CreatorName = 'creatorName',
    AssigneeName = 'assigneeName',
    Module = 'module',
    SalespersonReferenceCode = 'salespersonRefCode',
    Reference = 'reference',
    LastActivity = 'lastActivity',
    DateOfSubmission = 'dateOfSubmission',
    DateOfApproval = 'dateOfApproval',
    AppointmentDate = 'appointmentDate',
    MobilityDate = 'mobilityDate',
    DurationSubmittedToApprovedDeclined = 'durationSubmittedToApprovedDeclined',
    EventDisplayName = 'eventDisplayName',
}

export type SystemMainColumnSetting = SystemApplicationColumnSetting & {
    key: SystemMainSettingKey;
};

type GetSystemMainHeaderProps = {
    id?: string;
    createdAt: string;
    lastActivity: string;
    status: string;
    timeZone?: string;
    t: TFunction;
};

type SystemMainKey = { [key in SystemMainSettingKey]: SystemMainColumnSetting };

const getSystemMainSetting = (stage: ApplicationStage, header: GetSystemMainHeaderProps) => {
    const { t } = header;
    const allSettings: SystemMainColumnSetting[] = [
        {
            key: SystemMainSettingKey.Status,
            header: header.status,
            getCellValue: ({ support: { appStage } }, { t }) => {
                const { status } = appStage.value;

                // Some cases, like "otpCompleted" saved in database,
                // but "translation" record have "OTPCompleted" as key
                if (status === ApplicationStatus.OTPCompleted) {
                    return t('applicationList:status.OTPCompleted') as string;
                }

                return status ? (t(`applicationList:status.${upperFirst(appStage.value.status)}`) as string) : '';
            },
        },
        {
            key: SystemMainSettingKey.CreatedAt,
            header: header.createdAt,
            getCellValue: ({ _id, _versioning, support }, { t }) =>
                getFormattedDate(t, _versioning.createdAt, support.company?.timeZone),
        },
        {
            key: SystemMainSettingKey.DealerName,
            header: t('applicationExcel:main.dealer'),
            getCellValue: ({ support: { dealer } }) => dealer?.displayName ?? '',
        },
        {
            key: SystemMainSettingKey.CreatorName,
            header: t('applicationExcel:main.createdBy'),
            getCellValue: (preparedData, { t }) =>
                preparedData.support.creatorName
                    ? getAuthorText(t, preparedData._versioning.createdBy.kind, preparedData.support.creatorName)
                    : '',
        },
        {
            key: SystemMainSettingKey.AssigneeName,
            // eslint-disable-next-line max-len
            header: `${stage === ApplicationStage.Mobility ? t('applicationExcel:main.assignee') : t('applicationExcel:main.salesperson')}`,
            getCellValue: ({ support: { assignee }, ...data }) => assignee?.displayName ?? '',
        },
        stage !== ApplicationStage.Mobility && {
            key: SystemMainSettingKey.SalespersonReferenceCode,
            header: t('applicationExcel:main.salespersonRefCode'),
            getCellValue: ({ support: { assignee } }) => assignee?.alias ?? '',
        },
        {
            key: SystemMainSettingKey.Module,
            header: t('applicationExcel:main.module'),
            getCellValue: ({ support: { module } }) => module.displayName,
        },
        stage !== ApplicationStage.Mobility && {
            key: SystemMainSettingKey.Reference,
            header: t('applicationExcel:main.otherApplicationReference'),
            getCellValue: ({ support, stages }, { t }) => {
                const filter = support.referenceApplications.flatMap(app => {
                    const list = stages.filter(listStage => listStage !== stage);

                    const reference = list
                        .map(listStage => {
                            if (!isNil(app?.[`${listStage}Stage`])) {
                                return app?.[`${listStage}Stage`]?.identifier;
                            }

                            return null;
                        })
                        .filter(Boolean);

                    return reference;
                });

                return filter.length ? uniq(filter).join(', ') : '';
            },
        },
        {
            key: SystemMainSettingKey.LastActivity,
            header: header.lastActivity,
            getCellValue: ({ _versioning, support: { editorName, company } }, { t }) => {
                const formattedEditorName = getAuthorText(t, _versioning.updatedBy.kind, editorName);

                return getLastModifiedUser(t, _versioning.updatedAt, formattedEditorName, company?.timeZone);
            },
        },
        (stage === ApplicationStage.Financing ||
            stage === ApplicationStage.Insurance ||
            stage === ApplicationStage.Mobility) && {
            key: SystemMainSettingKey.DateOfSubmission,
            header: header.timeZone
                ? t('applicationExcel:main.dateSubmissionResubmissionWithTimeZone', {
                      timeZone: getTimeZoneOffset(header.t, header.timeZone),
                  })
                : t('applicationExcel:main.dateSubmissionResubmission'),
            getCellValue: ({ support, _id, kind }, { t }) => {
                const trails = support?.submittedAuditTrails ?? [];

                const trail = trails?.find(({ applicationId, _kind }) => {
                    // For mobility the audit trail is different
                    if (kind === ApplicationKind.Mobility) {
                        return (
                            applicationId.equals(_id) &&
                            (_kind === AuditTrailKind.BookingSubmitted || _kind === AuditTrailKind.BookingAmended)
                        );
                    }

                    return (
                        applicationId.equals(_id) &&
                        // As for internal application, detect the submitted to system
                        (_kind === AuditTrailKind.ApplicationSubmittedToSystem ||
                            _kind === AuditTrailKind.ApplicationResubmittedToSystem)
                    );
                });

                return trail?._date ? getFormattedDate(t, trail?._date, support.company?.timeZone) : '';
            },
        },
        stage === ApplicationStage.Financing && {
            key: SystemMainSettingKey.DateOfApproval,
            header: header.timeZone
                ? t('applicationExcel:main.dateOfApprovalWithTimeZone', {
                      timeZone: getTimeZoneOffset(header.t, header.timeZone),
                  })
                : t('applicationExcel:main.dateOfApproval'),
            getCellValue: ({ _id, support }, { t }) => {
                const approvalApplication = support?.applicationApproval?.find(app => app.applicationId.equals(_id));

                return approvalApplication
                    ? getFormattedDate(t, approvalApplication?._date, support.company?.timeZone)
                    : '';
            },
        },
        stage === ApplicationStage.Financing && {
            key: SystemMainSettingKey.DurationSubmittedToApprovedDeclined,
            header: t('applicationExcel:main.dateOfApproval'),
            getCellValue: ({ support, _id, kind }, { t }) => {
                const trails = support?.submittedAuditTrails;
                const trail = trails?.find(
                    ({ applicationId, _kind }) =>
                        applicationId.equals(_id) &&
                        // As for internal application, detect the submitted to system
                        (_kind === AuditTrailKind.ApplicationSubmittedToSystem ||
                            _kind === AuditTrailKind.ApplicationResubmittedToSystem)
                );

                const approvalApplication = support?.applicationApproval?.find(app => app.applicationId.equals(_id));

                const diff = approvalApplication?._date
                    ? dayjs(approvalApplication?._date).diff(trail?._date, 'hour')
                    : null;
                const days = Math.floor(diff / 24);
                const hours = diff % 24;

                return `${t('applicationList:approvalDate.label', { days, hours })}`;
            },
        },
    ];

    return [keyBy('key', allSettings) as SystemMainKey, allSettings] as const;
};

export default getSystemMainSetting;
