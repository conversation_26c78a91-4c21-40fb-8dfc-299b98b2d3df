import { Col, Row, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ConfiguratorModuleSpecsFragment } from '../../../../api/fragments/ConfiguratorModuleSpecs';
import { useListUsersQuery } from '../../../../api/queries/listUsers';
import { EventMediumType, ModuleType } from '../../../../api/types';
import { useDealerContext } from '../../../../components/contexts/DealerContextManager';
import CascaderField from '../../../../components/fields/CascaderField';
import DealerDepositAmountField from '../../../../components/fields/DealershipFields/DealerDepositAmountField';
import DealershipCascaderField from '../../../../components/fields/DealershipFields/DealershipCascaderField';
import DealershipPriceDisclaimerField from '../../../../components/fields/DealershipFields/DealershipPriceDisclaimer';
import DealershipSelectField from '../../../../components/fields/DealershipFields/DealershipSelectField';
import DealershipTranslatedStringField from '../../../../components/fields/DealershipFields/DealershipTranslatedString';
// eslint-disable-next-line max-len
import DealershipTranslatedTextAreaField from '../../../../components/fields/DealershipFields/DealershipTranslatedTextArea';
import InputField from '../../../../components/fields/InputField';
import SelectField from '../../../../components/fields/SelectField';
import SwitchField from '../../../../components/fields/SwitchField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import FormFields from '../../../../themes/admin/Fields/FormFields';
import useEventOptions from '../../../../utilities/useEventOptions';
import useLeadGenTooltip from '../../../../utilities/useLeadGenTooltip';
import useMarkDownInfoTooltip from '../../../../utilities/useMarkDownInfoTooltip';
import useMyInfoOption from '../../../../utilities/useMyInfoOption';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import useBankOptions from '../../FinanceProductDetailsPage/shared/useBankOptions';
import useScenarioObserver from '../StandardApplicationModulePage/useScenarioObserver';
import useScenarioOptions from '../StandardApplicationModulePage/useScenarioOptions';
import MarketTypeGroup from '../modules/implementations/shared/MarketTypeGroup';
import useDisplayPreferenceOptions from '../modules/implementations/shared/useDisplayPreferenceOptions';
import useFinancingPreferenceOptions from '../modules/implementations/shared/useFinancingPreferenceOptions';
import useInsurerOptions from '../modules/implementations/shared/useInsurerOptions';
import useModuleOptions from '../modules/implementations/shared/useModuleOptions';
import { type ConfiguratorModuleConfigurationFormValues } from './type';

type ConfiguratorModuleConfigurationFormProps = {
    module: ConfiguratorModuleSpecsFragment;
};

const ConfiguratorModuleConfigurationForm = ({ module }: ConfiguratorModuleConfigurationFormProps) => {
    const { t } = useTranslation(['configuratorModuleDetail', 'moduleDetails']);
    const { yesNoSwitch } = useSystemSwitchData();
    const { myInfoOptions, hasMyInfoModule } = useMyInfoOption();
    const { dealersFromApi } = useDealerContext();
    const { values, setFieldValue } = useFormikContext<ConfiguratorModuleConfigurationFormValues>();

    const options = useModuleOptions(module.company, { addNoneOptions: { promoCodes: true, payment: true } });

    const markdownInfoTooltip = useMarkDownInfoTooltip();
    const displayPreferenceOptions = useDisplayPreferenceOptions();
    const financingPreferenceOptions = useFinancingPreferenceOptions();
    const { eventLeadOriginTypeOptions, eventMediumTypeOptions } = useEventOptions();
    const definedFieldsTooltip = useLeadGenTooltip('definedFields');
    const { insurerList, loading: insurerLoading } = useInsurerOptions(module.company.id);

    const { options: bankOptions } = useBankOptions();

    const { data, loading } = useListUsersQuery({
        fetchPolicy: 'cache-and-network',
    });

    const salesPersonOptions = useMemo(
        () =>
            (data?.list?.items || []).map(user => ({
                label: user.displayName,
                value: user.id,
            })),
        [data]
    );

    const { scenarioOptions } = useScenarioOptions();
    const scenario = useScenarioObserver(ModuleType.ConfiguratorModule);

    const hasPorscheIdModule = useMemo(
        () => module.company.modules.some(module => module.__typename === ModuleType.PorscheIdModule),
        [module.company.modules]
    );

    useEffect(() => {
        if (values.isCustomerDataRetreivalByPorscheId && isNil(values.isPorscheIdLoginMandatory)) {
            setFieldValue('isPorscheIdLoginMandatory', true);
        }
        if (!values.isCustomerDataRetreivalByPorscheId) {
            setFieldValue('isPorscheIdLoginMandatory', null);
        }
    }, [setFieldValue, values.isCustomerDataRetreivalByPorscheId, values.isPorscheIdLoginMandatory]);

    return (
        <CollapsibleWrapper defaultActiveKey="configuratorModuleDetail_mainDetails">
            <Panel
                key="configuratorModuleDetail_mainDetails"
                header={
                    <Typography.Title level={5}>{t('configuratorModuleDetail:subTitles.mainDetails')}</Typography.Title>
                }
            >
                <Row gutter={10}>
                    <Col md={8} xs={24}>
                        <FormFields.InputField
                            {...t('configuratorModuleDetail:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Col>
                    {hasMyInfoModule && (
                        <Col md={8} xs={24}>
                            <CascaderField
                                {...t('configuratorModuleDetail:fields.myInfoSetting', { returnObjects: true })}
                                name="myInfoSettingId"
                                options={myInfoOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    {options.cap.length ? (
                        <Col md={8} xs={24}>
                            <FormFields.SelectField
                                allowClear
                                {...t('moduleDetails:fields.capModuleId', { returnObjects: true })}
                                name="capModuleId"
                                onChange={value => {
                                    if (!value || !values.capPrequalification) {
                                        setFieldValue('capPrequalification', false);
                                    }
                                }}
                                options={options.cap}
                                showSearch
                            />
                        </Col>
                    ) : null}

                    {values.capModuleId && (
                        <>
                            {/* 
                            Based on https://appvantage.atlassian.net/browse/AN-2545 need to hide this form
                            This form will be re-enabled on next phase 
                        */}

                            {/* <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isSearchCapCustomerOptional', { returnObjects: true })}
                                name="isSearchCapCustomerOptional"
                            />
                        </Col> */}

                            <Col md={8} xs={24}>
                                <FormFields.SwitchField
                                    {...yesNoSwitch}
                                    {...t('moduleDetails:fields.prequalification', { returnObjects: true })}
                                    name="capPrequalification"
                                />
                            </Col>

                            <Col md={8} xs={24}>
                                <FormFields.SelectField
                                    {...t('moduleDetails:fields.capLeadOrigin', { returnObjects: true })}
                                    name="leadMedium"
                                    options={eventMediumTypeOptions}
                                    tooltip={definedFieldsTooltip}
                                    required
                                    showSearch
                                />
                            </Col>

                            <Col md={8} xs={24}>
                                <FormFields.SelectField
                                    {...t('moduleDetails:fields.capLeadSource', { returnObjects: true })}
                                    name="leadOrigin"
                                    options={eventLeadOriginTypeOptions}
                                    tooltip={definedFieldsTooltip}
                                    required
                                    showSearch
                                />
                            </Col>

                            <Col md={8} xs={24}>
                                <FormFields.InputField
                                    {...t('moduleDetails:fields.capCampaignId', { returnObjects: true })}
                                    name="leadCampaignId"
                                    required={values.leadMedium !== EventMediumType.Walkin}
                                />
                            </Col>
                        </>
                    )}

                    {hasPorscheIdModule && (
                        <>
                            <Col md={8} xs={24}>
                                <SwitchField
                                    {...t('moduleDetails:fields.isCustomerDataRetreivalByPorscheId', {
                                        returnObjects: true,
                                    })}
                                    {...yesNoSwitch}
                                    name="isCustomerDataRetreivalByPorscheId"
                                />
                            </Col>
                            {values.isCustomerDataRetreivalByPorscheId && (
                                <Col md={8} xs={24}>
                                    <SwitchField
                                        {...t('moduleDetails:fields.isPorscheIdLoginMandatory', {
                                            returnObjects: true,
                                        })}
                                        {...yesNoSwitch}
                                        name="isPorscheIdLoginMandatory"
                                    />
                                </Col>
                            )}
                        </>
                    )}

                    <Col md={8} xs={24}>
                        <CascaderField
                            {...t('configuratorModuleDetail:fields.liveChatSetting', { returnObjects: true })}
                            name="liveChatSettingId"
                            options={options.liveChat}
                            showSearch
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <FormFields.SelectField
                            {...t('configuratorModuleDetail:fields.scenario', { returnObjects: true })}
                            mode="multiple"
                            name="scenarios"
                            options={scenarioOptions}
                            showSearch
                        />
                    </Col>

                    {scenario.hasPayment && (
                        <>
                            <Col md={8} xs={24}>
                                <DealershipCascaderField
                                    {...t('configuratorModuleDetail:fields.paymentSetting', {
                                        returnObjects: true,
                                    })}
                                    name="paymentSetting"
                                    options={options?.payment || []}
                                    required
                                    showSearch
                                />
                            </Col>
                            <Col md={8} xs={24}>
                                <DealerDepositAmountField
                                    {...t('configuratorModuleDetail:fields.depositAmount', {
                                        returnObjects: true,
                                    })}
                                    name="depositAmount"
                                />
                            </Col>

                            <Col md={8} xs={24}>
                                <FormFields.SwitchField
                                    {...yesNoSwitch}
                                    {...t('moduleDetails:fields.skipForDeposit', { returnObjects: true })}
                                    name="skipForDeposit"
                                />
                            </Col>
                        </>
                    )}
                    <Col md={8} xs={24}>
                        <FormFields.SelectField
                            {...t('configuratorModuleDetail:fields.promoCodeModule', { returnObjects: true })}
                            name="promoCodeModuleId"
                            options={options.promoCodes}
                            showSearch
                        />
                    </Col>
                    {scenario.hasInsurance && (
                        <Col md={8} xs={24}>
                            <FormFields.SelectField
                                {...t('moduleDetails:fields.insurer', { returnObjects: true })}
                                loading={insurerLoading}
                                mode="multiple"
                                name="insurerIds"
                                options={insurerList}
                                showSearch
                            />
                        </Col>
                    )}
                    {scenario.hasFinancing && (
                        <Col md={8} xs={24}>
                            <FormFields.SelectField
                                {...t('moduleDetails:fields.bank', { returnObjects: true })}
                                mode="multiple"
                                name="bankIds"
                                options={bankOptions}
                                showSearch
                            />
                        </Col>
                    )}
                    {scenario.hasAppointment && (
                        <Col md={8} xs={24}>
                            <FormFields.SelectField
                                {...t('moduleDetails:fields.appointmentModuleId', { returnObjects: true })}
                                name="appointmentModuleId"
                                options={options.appointments}
                                required
                                showSearch
                            />
                        </Col>
                    )}

                    {scenario.hasAppointment && values.appointmentModuleId && (
                        <Col md={8} xs={24}>
                            <FormFields.SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.displayAppointmentDatepicker', { returnObjects: true })}
                                name="displayAppointmentDatepicker"
                            />
                        </Col>
                    )}

                    {!module.bankModuleId && values.showFinanceCalculator && (
                        <Col md={8} xs={24}>
                            <FormFields.SelectField
                                required
                                {...t('moduleDetails:fields.bankModuleId', { returnObjects: true })}
                                name="bankModuleId"
                                options={options.financing}
                                showSearch
                            />
                        </Col>
                    )}
                    {(values.showInsuranceCalculator || scenario.hasInsurance || scenario.hasLeadCapture) && (
                        <Col md={8} xs={24}>
                            <FormFields.SelectField
                                required
                                {...t('moduleDetails:fields.insuranceModuleId', { returnObjects: true })}
                                name="insuranceModuleId"
                                options={options.insurance}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col md={8} xs={24}>
                        <DealershipSelectField
                            {...t('configuratorModuleDetail:fields.assignee', { returnObjects: true })}
                            loading={loading}
                            name="assignee"
                            options={salesPersonOptions}
                            showSearch
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <FormFields.SwitchField
                            {...yesNoSwitch}
                            {...t('configuratorModuleDetail:fields.tradeIn', { returnObjects: true })}
                            name="tradeIn"
                        />
                    </Col>

                    {values?.tradeIn && (
                        <Col md={8} xs={24}>
                            <FormFields.SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isTradeInAmountVisible', { returnObjects: true })}
                                name="isTradeInAmountVisible"
                            />
                        </Col>
                    )}

                    <MarketTypeGroup name="market" required />

                    <Col md={8} xs={24}>
                        <DealershipPriceDisclaimerField
                            {...t('moduleDetails:fields.priceDisclaimerTitle', { returnObjects: true })}
                            name="priceDisclaimer"
                            isMultipleEntry
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <DealershipTranslatedStringField
                            {...t('moduleDetails:fields.termsTitle', { returnObjects: true })}
                            dealers={dealersFromApi}
                            name="termsTitle"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <DealershipTranslatedTextAreaField
                            {...t('moduleDetails:fields.termsText', { returnObjects: true })}
                            autoSize={{ minRows: 2, maxRows: 6 }}
                            companyId={module.company.id}
                            name="termsText"
                            tooltip={markdownInfoTooltip}
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...t('moduleDetails:fields.inventoryEnabled', { returnObjects: true })}
                            {...yesNoSwitch}
                            name="isInventoryEnabled"
                        />
                    </Col>

                    {scenario.hasFinancing && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.financingPreference', { returnObjects: true })}
                                disabled={scenario.hasOnlyFinancing}
                                name="financingPreference"
                                options={financingPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFinanceCalculator', { returnObjects: true })}
                            disabled={scenario.hasFinancing}
                            name="showFinanceCalculator"
                        />
                    </Col>

                    {scenario.hasInsurance && (
                        <Col md={8} xs={24}>
                            <SwitchField
                                {...yesNoSwitch}
                                {...t('moduleDetails:fields.isInsuranceOptional', { returnObjects: true })}
                                disabled={scenario.hasOnlyInsurance}
                                name="isInsuranceOptional"
                            />
                        </Col>
                    )}

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showInsuranceCalculator', { returnObjects: true })}
                            disabled={scenario.hasInsurance}
                            name="showInsuranceCalculator"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showResetKYCButton', { returnObjects: true })}
                            name="showResetKYCButton"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SwitchField
                            {...yesNoSwitch}
                            {...t('moduleDetails:fields.showFromValueOnVehicleDetails', { returnObjects: true })}
                            name="showFromValueOnVehicleDetails"
                        />
                    </Col>

                    <Col md={8} xs={24}>
                        <SelectField
                            {...t('moduleDetails:fields.bankDisplayPreference', { returnObjects: true })}
                            name="bankDisplayPreference"
                            options={displayPreferenceOptions}
                            showSearch
                        />
                    </Col>

                    {values.insurerIds.length >= 1 && (
                        <Col md={8} xs={24}>
                            <SelectField
                                {...t('moduleDetails:fields.insurerDisplayPreference', { returnObjects: true })}
                                name="insurerDisplayPreference"
                                options={displayPreferenceOptions}
                                showSearch
                            />
                        </Col>
                    )}

                    <Col lg={8} xs={24}>
                        <InputField
                            {...t('moduleDetails:fields.externalUrl', { returnObjects: true })}
                            addonBefore="https://"
                            name="externalUrl"
                            required
                        />
                    </Col>
                </Row>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default ConfiguratorModuleConfigurationForm;
