import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { LocalCustomerManagementModuleSpecsFragment } from '../../../../api/fragments';
import {
    UpdateLocalCustomerManagementKycFieldsDocument,
    UpdateLocalCustomerManagementKycFieldsMutation,
    UpdateLocalCustomerManagementKycFieldsMutationVariables,
} from '../../../../api/mutations/updateLocalCustomerManagementKYCFields';
import { AgeCalculationMethod } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import KYCFieldsForm from './KYCFieldsForm';
import { KYCFieldFormValues } from './shared';

export type KYCFieldsProps = { module: LocalCustomerManagementModuleSpecsFragment };

const KYCFields = ({ module }: KYCFieldsProps) => {
    const { t } = useTranslation('localCustomerModuleDetails');

    const initialValues = useMemo<KYCFieldFormValues>(
        () => ({
            kycFields: [...module.kycFields].sort((a, b) => a.order - b.order).map(item => item.field),
            minimumAge: !isNil(module?.extraSettings.minimumAge) ? module.extraSettings.minimumAge : 18,
            ageCalculationMethod: module?.extraSettings.ageCalculationMethod ?? AgeCalculationMethod.BirthdayBased,
            mobileVerification: module?.extraSettings.mobileVerification ?? false,
        }),
        [module]
    );

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError(
        async ({ kycFields, minimumAge, ageCalculationMethod, mobileVerification }: KYCFieldFormValues) => {
            // submitting message
            message.loading({
                content: t('localCustomerModuleDetails:kycFieldsForm.messages.updateKYCFieldsSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient
                .mutate<
                    UpdateLocalCustomerManagementKycFieldsMutation,
                    UpdateLocalCustomerManagementKycFieldsMutationVariables
                >({
                    mutation: UpdateLocalCustomerManagementKycFieldsDocument,
                    variables: {
                        moduleId: module.id,
                        kycFields: kycFields.map((field, index) => ({
                            field,
                            order: index + 1,
                            // TODO: temporarily set as true until FE draggable component implemented
                            isActive: true,
                        })),
                        extraSettings: {
                            minimumAge,
                            ageCalculationMethod,
                            mobileVerification,
                        },
                    },
                })
                .finally(() => message.destroy('primary'));

            // inform about success
            message.success({
                content: t('localCustomerModuleDetails:kycFieldsForm.messages.updateKYCFieldsSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit}>
            {({ handleSubmit }) => (
                <Form id="updateKYCFields" name="updateKYCFields" onSubmitCapture={handleSubmit}>
                    <KYCFieldsForm company={module.company} />
                </Form>
            )}
        </Formik>
    );
};

export default KYCFields;
