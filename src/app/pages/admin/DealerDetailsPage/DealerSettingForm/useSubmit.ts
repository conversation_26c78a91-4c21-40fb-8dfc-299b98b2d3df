import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { isEqual, pick } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { DealerFragmentFragment } from '../../../../api/fragments/DealerFragment';
import {
    CreateDealerSocialMediaDocument,
    CreateDealerSocialMediaMutation,
    CreateDealerSocialMediaMutationVariables,
} from '../../../../api/mutations/createDealerSocialMedia';
import {
    DeleteDealerSocialMediaDocument,
    DeleteDealerSocialMediaMutation,
    DeleteDealerSocialMediaMutationVariables,
} from '../../../../api/mutations/deleteDealerSocialMedia';
import {
    UpdateDealerDocument,
    UpdateDealerMutation,
    UpdateDealerMutationVariables,
} from '../../../../api/mutations/updateDealer';
import {
    UpdateDealerSocialMediaDocument,
    UpdateDealerSocialMediaMutation,
    UpdateDealerSocialMediaMutationVariables,
} from '../../../../api/mutations/updateDealerSocialMedia';
import useHandleError from '../../../../utilities/useHandleError';
import type { FormValues } from '../sections/shared';
import useHandleDealerAsset from '../useHandleDealerAsset';

const useSubmit = (dealer: DealerFragmentFragment) => {
    const { t } = useTranslation(['dealerForm']);
    const apolloClient = useApolloClient();
    const handleDealerAsset = useHandleDealerAsset();

    return useHandleError<FormValues>(
        async values => {
            message.loading({
                content: t('dealerForm:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient.mutate<UpdateDealerMutation, UpdateDealerMutationVariables>({
                mutation: UpdateDealerDocument,
                variables: {
                    id: dealer.id,
                    dealerInput: values.dealerInput,
                },
            });

            const { socialMedia } = values;
            const storedSocialMedia = dealer.contact.socialMedia;

            const addMedialist = socialMedia.filter(media => !media?.id);

            // loop stored social media
            const existingPromises = (storedSocialMedia || []).map(async storedMedia => {
                const currentMedia = socialMedia.find(media => 'id' in media && media.id === storedMedia.id);
                const pickWithFields = pick(['title', 'content']);

                // delete dealer social media
                if (!currentMedia) {
                    // delete dealer social media asset first
                    return handleDealerAsset(storedMedia.id, storedMedia.icon, dealer).then(() =>
                        // delete social media core
                        apolloClient.mutate<DeleteDealerSocialMediaMutation, DeleteDealerSocialMediaMutationVariables>({
                            mutation: DeleteDealerSocialMediaDocument,
                            variables: { dealerSocialMediaId: storedMedia.id },
                        })
                    );
                }

                // update the existing social media
                if (!isEqual(pickWithFields(storedMedia), pickWithFields(currentMedia))) {
                    // update the social media core
                    return apolloClient
                        .mutate<UpdateDealerSocialMediaMutation, UpdateDealerSocialMediaMutationVariables>({
                            mutation: UpdateDealerSocialMediaDocument,
                            variables: {
                                dealerSocialMediaId: currentMedia.id,
                                dealerSocialMediaInput: {
                                    content: currentMedia.content,
                                    title: currentMedia.title,
                                },
                            },
                        })
                        .then(() => handleDealerAsset(currentMedia.id, currentMedia.icon, dealer));
                }

                return handleDealerAsset(currentMedia.id, currentMedia.icon, dealer);
            });

            // get all newly added dealer social media
            const createPromises = addMedialist.map(async media => {
                const currentInput = { ...pick(['title', 'content'], media) };

                return apolloClient
                    .mutate<CreateDealerSocialMediaMutation, CreateDealerSocialMediaMutationVariables>({
                        mutation: CreateDealerSocialMediaDocument,
                        variables: { dealerId: dealer.id, dealerSocialMediaInput: currentInput },
                    })
                    .then(createMedia => handleDealerAsset(createMedia.data.createDealerSocialMedia.id, media.icon));
            });

            await Promise.all(existingPromises);
            await Promise.all(createPromises);

            message.success({
                content: t('dealerForm:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, dealer, handleDealerAsset, t]
    );
};

export default useSubmit;
