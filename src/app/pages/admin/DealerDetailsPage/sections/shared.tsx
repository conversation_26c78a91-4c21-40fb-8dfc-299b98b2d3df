import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments/UploadFileWithPreviewFormData';
import { CreateDealerMutationVariables } from '../../../../api/mutations/createDealer';
import { DealerSocialMediaInput } from '../../../../api/types';

export type FormValues = CreateDealerMutationVariables & {
    socialMedia: Array<DealerSocialMediaInput & { icon?: File | UploadFileWithPreviewFormDataFragment; id?: string }>;
};
