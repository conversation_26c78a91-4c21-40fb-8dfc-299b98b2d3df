import { useTranslation } from 'react-i18next';
import RadioGroupField from '../../../../../components/fields/RadioGroupField';
import useFinanceProductOptions from '../../shared/useFinanceProductOptions';

type ConfigurationProps = {
    disabled: boolean;
};

export const FinanceProductBasedOnField = ({ disabled }: ConfigurationProps) => {
    const { t } = useTranslation(['financeProductDetails']);

    const { financeProductBasedOnOptions } = useFinanceProductOptions();

    return (
        <RadioGroupField
            {...t('financeProductDetails:fields.configureBasedOn', {
                returnObjects: true,
            })}
            disabled={disabled}
            name="basedOn"
            options={financeProductBasedOnOptions}
            required
        />
    );
};
