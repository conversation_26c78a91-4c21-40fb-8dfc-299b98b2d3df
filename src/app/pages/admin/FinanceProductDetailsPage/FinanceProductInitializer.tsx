import { useFormikContext } from 'formik';
import { useEffect, useMemo } from 'react';
import {
    CalculationMode,
    DeriveMethod,
    FieldDisplaySetting,
    FinanceProductBasedOn,
    FinanceProductType,
} from '../../../api/types';
import { FinanceProductDetailProvider } from './FinanceProductDetailContext';
import initialFuelTax from './FinanceProductForm/helpers/initialFuelTax';
import initialLicenseTax from './FinanceProductForm/helpers/initialLicenseTax';
import { includesFinancing, includesLeasing } from './shared/groupings';
import { type FinanceProductFormValues, InterestRateType } from './shared/typings';
import useAlignUnits from './shared/useAlignUnits';
import useReset from './shared/useReset';
import useTable from './shared/useTable';
import useType from './shared/useType';
import useUnit from './shared/useUnit';
import useViewableAndEditable, {
    generalViewableAndEditableDefaults,
    interestRateViewableAndEditableDefaults,
} from './shared/useViewableAndEditable';
import useVisible from './shared/useVisible';

type FinanceProductInitializerProps = { children: JSX.Element; updated?: string };

/* provides default values for finance product fields */
const Effects = ({ updated }: Pick<FinanceProductInitializerProps, 'updated'>) => {
    const { values, setFieldValue } = useFormikContext<FinanceProductFormValues>();
    const type = values?.type;

    const visible = useVisible();

    // reset unrelated settings to undefined when finance product type changes
    useReset(type, setFieldValue);

    useEffect(() => {
        if (!updated) {
            if (includesFinancing(type)) {
                setFieldValue('deriveMethod', DeriveMethod.Formula);
                setFieldValue('basedOn', FinanceProductBasedOn.DownPayment);
                setFieldValue('payment.isViewable', false);
                setFieldValue('payment.isEditable', false);
            }

            if (includesLeasing(type)) {
                setFieldValue('payment', undefined);
                setFieldValue('calculationMode', undefined);
                setFieldValue('deriveMethod', DeriveMethod.LookupTable);
            }

            // When UCCL leasing used
            // Enforce calculation mode to be flat
            // While inside the field, it should be disabled
            if (type === FinanceProductType.UcclLeasing) {
                setFieldValue('calculationMode', CalculationMode.Flat);
            }
        }
    }, [type, setFieldValue, updated]);

    const {
        downPayment,
        loan,
        term,
        interestRate,
        lease,
        deposit,
        basedOn,
        licensePlateFee,
        commission,
        monthlyPaymentFixedInterestRate,
        displacement,
        licenseAndFuelTax,
        insuranceFee,
        taxLoss,
        balloon,
        residualValue,
        balloonGfv,
    } = values;

    useViewableAndEditable(
        'downPayment',
        !!visible.downPayment,
        downPayment?.type === 'table',
        downPayment?.isViewable,
        downPayment?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'loan',
        !!visible.downPayment,
        loan?.type === 'table',
        loan?.isViewable,
        loan?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'term',
        !!visible.term,
        false,
        term?.isViewable,
        term?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'interestRate',
        !!visible.interestRate,
        interestRate?.type === 'table',
        interestRate?.isViewable === FieldDisplaySetting.Hide,
        false,
        interestRateViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'balloon',
        !!visible.balloon,
        false,
        balloon?.isViewable,
        balloon?.isEditable,
        generalViewableAndEditableDefaults
    );
    useViewableAndEditable(
        'deposit',
        !!visible.deposit,
        deposit?.type === 'table',
        deposit?.isViewable,
        deposit?.isEditable,
        generalViewableAndEditableDefaults
    );

    // Initialize viewable and editable for UCCL
    useViewableAndEditable(
        'licensePlateFee',
        !!visible.licensePlateFee,
        false,
        licensePlateFee?.isViewable,
        licensePlateFee?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'commission',
        !!visible.commission,
        false,
        commission?.isViewable,
        commission?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'monthlyPaymentFixedInterestRate',
        !!visible.monthlyPaymentFixedInterestRate,
        false,
        monthlyPaymentFixedInterestRate?.isViewable,
        undefined,
        {
            isViewable: true,
        }
    );

    useViewableAndEditable(
        'displacement',
        !!visible.displacement,
        false,
        displacement?.isViewable,
        displacement?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'licenseAndFuelTax',
        !!visible.licenseAndFuelTax,
        false,
        licenseAndFuelTax?.isViewable,
        licenseAndFuelTax?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'insuranceFee',
        !!visible.insuranceFee,
        false,
        insuranceFee?.isViewable,
        insuranceFee?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'taxLoss',
        !!visible.taxLoss,
        false,
        taxLoss?.isViewable,
        taxLoss?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'residualValue',
        !!visible.residualValue,
        false,
        residualValue?.isViewable,
        residualValue?.isEditable,
        generalViewableAndEditableDefaults
    );

    useViewableAndEditable(
        'residualValue.averageMileage',
        !!visible.residualValue,
        false,
        residualValue?.averageMileage?.isViewable,
        residualValue?.averageMileage?.isEditable,
        generalViewableAndEditableDefaults
    );

    useType('downPayment', !!visible.downPayment, downPayment?.type);
    useType('loan', !!visible.loan, loan?.type);
    useType('term', true, term?.type);
    useType('interestRate', !!visible.interestRate, interestRate?.type, 'fixed');
    useType('lease', !!visible.lease, lease?.type, 'table');
    useType('deposit', !!visible.deposit, deposit?.type);

    useUnit(
        'downPayment',
        visible.downPayment && (downPayment?.type === 'range' || downPayment?.type === 'table'),
        downPayment?.unit
    );

    const useDownPayment = visible.downPayment && downPayment?.type === 'range';
    useUnit('downPayment', useDownPayment, downPayment?.minUnit, 'minUnit');
    useUnit('downPayment', useDownPayment, downPayment?.maxUnit, 'maxUnit');
    useUnit('downPayment', useDownPayment, downPayment?.stepUnit, 'stepUnit');
    useAlignUnits(
        'downPayment',
        useDownPayment,
        downPayment?.minUnit,
        downPayment?.maxUnit,
        downPayment?.stepUnit,
        'step',
        'stepUnit'
    );

    const useLoan = visible.loan && loan?.type === 'range';
    useUnit('loan', useLoan, loan?.unit);
    useUnit('loan', useLoan, loan?.minUnit, 'minUnit');
    useUnit('loan', useLoan, loan?.maxUnit, 'maxUnit');
    useUnit('loan', useLoan, loan?.stepUnit, 'stepUnit');
    useAlignUnits('loan', useLoan, loan?.minUnit, loan?.maxUnit, loan?.stepUnit, 'step', 'stepUnit');

    useUnit('deposit', visible.deposit, deposit?.unit);
    useUnit('interestRate', visible.interestRate && interestRate?.type === 'table', interestRate?.unit, 'unit');

    useTable('downPayment', visible.downPayment && downPayment?.type === 'table', downPayment?.table);

    useTable('interestRate', visible.interestRate && interestRate?.type === 'table', interestRate?.table);
    useTable('lease', visible.lease, lease?.table);
    useTable('deposit', visible.deposit && deposit?.type === 'table', lease?.table);

    useTable('residualValue', visible.residualValue, residualValue?.table);

    // Balloon
    useType('balloon', !!visible.balloon, balloon?.type, 'range');
    useUnit('balloon', visible.balloon && (balloon?.type === 'range' || balloon?.type === 'table'), balloon?.unit);
    useUnit('balloon', visible.balloon && balloon?.type === 'range', balloon?.minUnit, 'minUnit');
    useUnit('balloon', visible.balloon && balloon?.type === 'range', balloon?.maxUnit, 'maxUnit');
    useUnit('balloon', visible.balloon && balloon?.type === 'range', balloon?.stepUnit, 'stepUnit');
    useTable('balloon', visible.balloon, balloon?.table);

    useViewableAndEditable(
        'balloon',
        !!visible.balloon,
        false,
        balloon?.isViewable,
        balloon?.isEditable,
        generalViewableAndEditableDefaults
    );

    // Balloon GFV
    useTable('balloonGfv', visible.balloonGfv, balloonGfv?.table);

    // Initialize tables for UCCL
    useTable(
        'licenseAndFuelTax',
        !!visible.licenseAndFuelTax,
        values?.licenseAndFuelTax?.licenseTable,
        initialLicenseTax,
        'licenseTable'
    );
    useTable(
        'licenseAndFuelTax',
        !!visible.licenseAndFuelTax,
        values?.licenseAndFuelTax?.fuelTable,
        initialFuelTax,
        'fuelTable'
    );
    useTable('insuranceFee', !!visible.insuranceFee, values?.insuranceFee?.table);

    useEffect(() => {
        if (interestRate?.type === InterestRateType.Table) {
            setFieldValue('interestRate.basedOn', basedOn);
        }

        if (basedOn === FinanceProductBasedOn.DownPayment && downPayment?.isViewable) {
            setFieldValue('downPayment.isEditable', true);
        }

        if (basedOn === FinanceProductBasedOn.Loan && loan?.isViewable) {
            setFieldValue('loan.isEditable', true);
        }
    }, [interestRate?.type, basedOn, downPayment, loan, setFieldValue]);

    // Take out term interestOnly
    // If term's inside the useEffect all of them
    // when we type change term values will break the browser
    const termInterestOnly = useMemo(() => term?.interestOnly, [term]);
    useEffect(() => {
        if (type !== FinanceProductType.DeferredPrincipal && termInterestOnly) {
            setFieldValue('term.interestOnly', undefined);
        }
    }, [type, setFieldValue, termInterestOnly]);

    return null;
};

const FinanceProductInitializer = ({ children, updated }: FinanceProductInitializerProps) => {
    const { values } = useFormikContext<FinanceProductFormValues>();

    return (
        <FinanceProductDetailProvider bankId={values.bankId}>
            <Effects updated={updated} />
            {children}
        </FinanceProductDetailProvider>
    );
};

export default FinanceProductInitializer;
