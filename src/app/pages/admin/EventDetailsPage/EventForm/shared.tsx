import { Dayjs } from 'dayjs';
import styled, { css } from 'styled-components';
import { EventInput, DealershipSettingInput } from '../../../../api/types';
import { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import { PageType } from '../../../shared/EventList/RenderShareUrl';
import type { BannerFormikValue } from '../../BannerDetailsPage/shared';
import { EventApplicationModuleConfirmEmailContents } from '../../ModuleDetailsPage/EventApplicationModulePage/type';
import { EventKycFormInput } from '../EventKycFields/types';

export type EventEmailFormValues = {
    submitOrder: EventApplicationModuleConfirmEmailContents;
};

export type EventFormValues = Omit<EventInput, 'period' | 'eventLevelEmailSettings' | 'hasVehicleIntegration'> & {
    eventLevelEmailSettings: EventEmailFormValues;
    dealerVariantsMapping?: { [dealerId: string]: string[] };
    period: {
        start: Dayjs;
        end: Dayjs;
    };
    startTime?: Dayjs;
    endTime?: Dayjs;
    moduleId?: string;
    banner: BannerFormikValue;
    excludeVehicleOfInterest: boolean;
    moduleLevelMobileVerification?: boolean;
} & EventKycFormInput;

export type CreateEventFormValues = EventFormValues & {
    moduleId: string;
};

export const EventInnerStyledPanel = styled(Panel)<{ $pageType: PageType }>`
    ${props =>
        props.$pageType === 'Admin' &&
        css`
            background: white;
            margin-bottom: 10px;
            &.ant-collapse-item {
                border: 0;
            }
        `}
`;

export const defaultDealershipSetting: DealershipSettingInput = {
    defaultId: undefined,
    overrides: [],
};
export { useCreateEventFormValidator, useEventFormValidator } from './useEventFormValidator';
