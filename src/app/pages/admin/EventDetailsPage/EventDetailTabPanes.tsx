import { EventDataFragment } from '../../../api/fragments/EventData';
import { ApplicationStage } from '../../../api/types';
import { PageType } from '../../shared/EventList/RenderShareUrl';
import EventCustomerInfoTab from './EventCustomerInfoTab';
import EventApplicationTabList, { ListKind } from './EventForm/EventApplicationTabList';
import EventInnerForm from './EventForm/EventInnerForm';
import Tab from './shared';

export type EventDetailTabPanesProps = {
    pageType: PageType;
    activeTab: Tab;
    goBack: () => void;
    event: EventDataFragment;
    disabled?: boolean;
    setLoadedAppointmentIds: React.Dispatch<React.SetStateAction<string[]>>;
    setSelectedAppointments: React.Dispatch<React.SetStateAction<string[]>>;
    setLoadedReservationIds: React.Dispatch<React.SetStateAction<string[]>>;
    setSelectedReservations: React.Dispatch<React.SetStateAction<string[]>>;
    setLoadedLeadIds: React.Dispatch<React.SetStateAction<string[]>>;
    setSelectedLeads: React.Dispatch<React.SetStateAction<string[]>>;
};
const EventDetailTabPanes = ({
    pageType,
    activeTab,
    goBack,
    event,
    disabled = false,
    setLoadedAppointmentIds,
    setSelectedAppointments,
    setLoadedReservationIds,
    setSelectedReservations,
    setLoadedLeadIds,
    setSelectedLeads,
}: EventDetailTabPanesProps) => {
    switch (activeTab) {
        case Tab.MainDetails:
            return <EventInnerForm disabled={disabled} event={event} goBack={goBack} pageType={pageType} />;

        case Tab.CustomerInformation:
            return <EventCustomerInfoTab disabled={disabled} pageType="Admin" />;

        case Tab.Reservations:
            return (
                <EventApplicationTabList
                    eventId={event.id}
                    listKind={ListKind.Applications}
                    setLoadedApplicationIds={setLoadedReservationIds}
                    setSelectedApplications={setSelectedReservations}
                    stage={ApplicationStage.Reservation}
                />
            );

        case Tab.Leads:
            return (
                <EventApplicationTabList
                    eventId={event.id}
                    listKind={ListKind.Leads}
                    setLoadedLeadIds={setLoadedLeadIds}
                    setSelectedLeads={setSelectedLeads}
                />
            );

        case Tab.Appointments:
            return (
                <EventApplicationTabList
                    eventId={event.id}
                    listKind={ListKind.Applications}
                    setLoadedApplicationIds={setLoadedAppointmentIds}
                    setSelectedApplications={setSelectedAppointments}
                    stage={ApplicationStage.Appointment}
                />
            );

        default:
            return null;
    }
};

export default EventDetailTabPanes;
