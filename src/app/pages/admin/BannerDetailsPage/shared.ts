import { UploadFileWithPreviewFormDataFragment } from '../../../api/fragments/UploadFileWithPreviewFormData';
import { BannerInput } from '../../../api/types';

export type BannerFormikValue = BannerInput & {
    moduleId?: string;
    // as of 14/02/2022, we support for single banner, in future we might enhance to multiple images

    bannerImage?: File | UploadFileWithPreviewFormDataFragment;
    mobileBannerImage?: File | UploadFileWithPreviewFormDataFragment;
};
