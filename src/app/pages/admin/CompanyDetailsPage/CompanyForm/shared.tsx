import { UploadFileFormDataFragment } from '../../../../api/fragments/UploadFileFormData';
import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments/UploadFileWithPreviewFormData';
import {
    CompanySettings,
    EdmEmailFooterPayload,
    EdmEmailSocialMediaPayload,
    MfaSettingsType,
} from '../../../../api/types';

export type EdmSocialMediaType = EdmEmailSocialMediaPayload & {
    icon?: File | UploadFileWithPreviewFormDataFragment;
    id?: string;
};

export type CompanyFormValues = Omit<CompanySettings, 'mfaSettings' | 'edmEmailFooter'> & {
    logo?: File | UploadFileWithPreviewFormDataFragment;
    logoNonWhiteBackground?: File | UploadFileWithPreviewFormDataFragment;
    mobileLogo?: File | UploadFileWithPreviewFormDataFragment;
    favicon?: File | UploadFileWithPreviewFormDataFragment;
    font?: File | UploadFileFormDataFragment;
    fontBold?: File | UploadFileFormDataFragment;
    mfaSettings: {
        type: MfaSettingsType;
        enabled: boolean;
    };
    edmEmailFooter: EdmEmailFooterPayload & {
        socialMedia: Array<EdmSocialMediaType>;
        emailIcon?: File | UploadFileWithPreviewFormDataFragment;
        arrowIcon?: File | UploadFileWithPreviewFormDataFragment;
        phoneIcon?: File | UploadFileWithPreviewFormDataFragment;
    };
};

export const defaultSocialMediaValue: EdmSocialMediaType = { altText: '', url: '', icon: null };
