import { UploadFileFormDataFragment } from '../../../../api/fragments/UploadFileFormData';
import { UploadFileWithPreviewFormDataFragment } from '../../../../api/fragments/UploadFileWithPreviewFormData';
import { BankIntegrationProvider, BankIntegrationSettings, BankSettings, SystemBank } from '../../../../api/types';

export { default as FinanceProductType } from './FinanceProductType';
export { default as BankIntegration } from './BankIntegration';

export type BankFormValues = BankSettings & {
    logo?: File | UploadFileWithPreviewFormDataFragment;
    font?: File | UploadFileFormDataFragment;
};

export type NewBankFormValues = BankFormValues & {
    availableFinanceProductTypes: SystemBank['availableFinanceProductTypes'];
    showMarginOfFinance: boolean;
    integrationProvider: BankIntegrationProvider;
    integrationSettings: BankIntegrationSettings;
};
