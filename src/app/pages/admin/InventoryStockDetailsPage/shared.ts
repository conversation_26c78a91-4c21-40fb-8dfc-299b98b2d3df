import { InventoryDetailsDataFragment } from '../../../api/fragments/InventoryDetailsData';
import { StockInventorySpecsFragment } from '../../../api/fragments/StockInventorySpecs';

type ConfiguratorStock = Extract<StockInventorySpecsFragment, { __typename: 'ConfiguratorStockInventory' }>;
type MobilityStock = Extract<StockInventorySpecsFragment, { __typename: 'MobilityStockInventory' }>;
export type StockFormValues = MobilityStock | ConfiguratorStock;
export type InventoryStockFormValues = InventoryDetailsDataFragment & { stock: StockFormValues };
