import { Route, Routes } from 'react-router-dom';
// eslint-disable-next-line max-len
import { MobilityApplicationEntrypointContextDataFragment } from '../../../api/fragments/MobilityApplicationEntrypointContextData';
import { useRouter } from '../../../components/contexts/shared';
import useTranslatedString from '../../../utilities/useTranslatedString';
import MetaData from '../../shared/MetaData';
import RedirectAndReplace from '../../shared/RedirectAndReplace';
import NotFoundPage from '../NotFoundPage';
import CarDetailsPage from './CarDetailsPage';
import CarListingPage from './CarListingPage';
import GiftCodeJourney from './GiftCodeJourney';
import Journey from './Journey';

export type MobilityApplicationEntrypointProps = {
    endpoint: MobilityApplicationEntrypointContextDataFragment;
};

const MobilityApplicationEntrypoint = ({ endpoint }: MobilityApplicationEntrypointProps) => {
    const router = useRouter();
    const translated = useTranslatedString();

    return (
        <>
            <MetaData title={`${translated(router.company.legalName)} : ${endpoint.displayName}`} />
            <Routes>
                <Route key="list" element={<CarListingPage endpoint={endpoint} />} path="" />
                <Route key="apply" element={<Journey endpoint={endpoint} />} path="apply/*" />
                <Route key="giftCode" element={<GiftCodeJourney endpoint={endpoint} />} path="giftCode/*" />
                <Route key="details" element={<CarDetailsPage endpoint={endpoint} />} path="details/:stockId" />
                <Route
                    key="sessionRevoked"
                    element={<RedirectAndReplace path=".." state={null} />}
                    path="sessionRevoked"
                />
                <Route key="404" element={<NotFoundPage />} path="*" />
            </Routes>
        </>
    );
};

export default MobilityApplicationEntrypoint;
