import { ComponentType, Dispatch, PropsWithChildren } from 'react';
import { DefaultLayoutProps } from '../../../../../themes/default/Standard/Layout';
import { GiftVoucherAction, GiftVoucherState } from '../reducer';

export type GiftVoucherAdyenPaymentPageProps = {
    state: GiftVoucherState;
    dispatch: Dispatch<GiftVoucherAction>;
    CustomLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    promoCodeModuleId?: string;
    applicationModuleId?: string;
};
