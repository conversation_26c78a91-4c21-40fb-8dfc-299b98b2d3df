import { Formik } from 'formik';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../../../themes/hooks';
import useHandleError from '../../../../../utilities/useHandleError';
import useValidator from '../../../../../utilities/useValidator';
import useAgreementsValidator from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import Inner, { AdyenFormValues } from './Inner';
import type { GiftVoucherAdyenPaymentPageProps } from './shared';
import useGiftVoucherAdyenDepositSubmission from './useGiftVoucherAdyenDepositSubmission';
import useGiftVoucherPaymentAgreements from './useGiftVoucherPaymentAgreements';

const GiftVoucherAdyenPaymentPage = ({ state, dispatch, CustomLayout, ...props }: GiftVoucherAdyenPaymentPageProps) => {
    const { token } = state;
    const { t } = useTranslation(['paymentDetails']);
    const { notification } = useThemeComponents();
    const paymentAgreements = useGiftVoucherPaymentAgreements(state);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const submitAdyenDeposit = useGiftVoucherAdyenDepositSubmission();
    const [adyenSessionResult, setSessionResult] = useState(null);

    const onSubmit = useHandleError(
        async (values: AdyenFormValues) => {
            notification.loading({
                content: t('paymentDetails:messages.paymentSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const { promoCodeId, ...agreementValues } = values;
            // gift voucher should not skip deposit
            const result = await submitAdyenDeposit(token, agreementValues, false, adyenSessionResult).finally(() => {
                notification.destroy('primary');
            });

            const { giftVoucher } = result;

            dispatch({ type: 'next', token: result.token, giftVoucher });
        },
        [notification, t, submitAdyenDeposit, token, adyenSessionResult, dispatch],
        {}
    );

    const onChangeSessionResult = useCallback((newSessionData: string) => {
        setSessionResult(newSessionData);
    }, []);

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.giftVoucher.deposit.amount > 0 ? validation : null}
        >
            <Inner
                CustomLayout={CustomLayout}
                adyenSessionResult={adyenSessionResult}
                dispatch={dispatch}
                onChangeSessionResult={onChangeSessionResult}
                state={state}
                {...props}
            />
        </Formik>
    );
};

export default GiftVoucherAdyenPaymentPage;
