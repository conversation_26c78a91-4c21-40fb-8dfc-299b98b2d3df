import { concat, isFinite, isNil, uniq, uniqBy } from 'lodash/fp';
import { createContext, useContext, useEffect, useMemo, useReducer, useRef } from 'react';
import type {
    FinderApplicationEntrypointContextDataFragment,
    FinderVehicleSelectionDataFragment,
} from '../../../../api/fragments';
import { useListFinderVehiclesForSelectionQuery } from '../../../../api/queries/listFinderVehiclesForSelection';
import {
    FinderVehicleCondition,
    FinderVehicleSortingField,
    FinderVehicleStatus,
    Purpose,
    SortingOrder,
    type FinderVehicleFilters,
} from '../../../../api/types';
import PortalLoadingElement from '../../../../components/PortalLoadingElement';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useDealerContext, useSingleDealerId } from '../../../../components/contexts/DealerContextManager';
import { useFinderLeadContext } from '../../FinderApplicationPublicAccessEntrypoint/FinderLeadContext';
import { PageState } from '../../MobilityApplicationEntrypoint/CarListingPage/CarListingStateProvider';
import useFinderLeadFilters from '../hooks/useFinderLeadFilters';
import { rangeToString } from './CarFilter/MonthlyPaymentFilter';
import { ActionMethods, CarListingContext } from './shared';

type State = PageState & {
    loading: boolean;

    /* Open or Close filter drawer. will has no effect if [showFilterDrawer] is false. */
    openFilterDrawer: boolean;

    /* Controls filter drawer's visibility */
    showFilterDrawer: boolean;

    total: number;
    scroll: boolean;
    variants: FinderVehicleSelectionDataFragment[];
    filters: FinderVehicleFilters;
    conditionFilter: string[];
    modelFilter: string[];
    rangeMonthlyPaymentFilter: string[];
};

type setPage = { type: 'setPage'; page: number };
type setPageSize = { type: 'setPageSize'; pageSize: number };
type setLoading = { type: 'setLoading'; loading: boolean };
type setUpdatePage = { type: 'setScroll'; scroll: boolean };
type setOpenFilterDrawer = { type: 'setOpenFilterDrawer'; openFilterDrawer: boolean };
type setTotal = { type: 'setTotal'; total: number };
type setVariants = { type: 'setVariants'; variants: FinderVehicleSelectionDataFragment[] };
type setFilters = { type: 'setFilters'; filters: FinderVehicleFilters };
type setConditionFilter = { type: 'setConditionFilter'; conditionFilter: string[] };
type setModelFilter = { type: 'setModelFilter'; modelFilter: string[] };
type setRangeMonthlyPaymentFilter = {
    type: 'setRangeMonthlyPaymentFilter';
    rangeMonthlyPaymentFilter: string[];
};
type setShowFilterDrawer = { type: 'setShowFilterDrawer'; showFilterDrawer: boolean };

type Action =
    | setPage
    | setPageSize
    | setLoading
    | setUpdatePage
    | setOpenFilterDrawer
    | setTotal
    | setVariants
    | setFilters
    | setConditionFilter
    | setModelFilter
    | setRangeMonthlyPaymentFilter
    | setShowFilterDrawer;

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'setPage':
            return { ...state, page: action.page };

        case 'setPageSize':
            return { ...state, page: 1, pageSize: action.pageSize };

        case 'setLoading':
            return { ...state, loading: action.loading };

        case 'setScroll':
            return { ...state, scroll: action.scroll };

        case 'setOpenFilterDrawer':
            return { ...state, openFilterDrawer: action.openFilterDrawer };

        case 'setTotal':
            return { ...state, total: action.total };

        case 'setVariants':
            return { ...state, variants: action.variants };

        case 'setFilters':
            return { ...state, filters: action.filters };

        case 'setConditionFilter':
            return { ...state, conditionFilter: action.conditionFilter };

        case 'setModelFilter':
            return { ...state, modelFilter: action.modelFilter };

        case 'setRangeMonthlyPaymentFilter':
            return { ...state, rangeMonthlyPaymentFilter: action.rangeMonthlyPaymentFilter };

        case 'setShowFilterDrawer':
            return { ...state, showFilterDrawer: action.showFilterDrawer };

        default:
            return state;
    }
};

const useFinderVehicleListReducer = () =>
    useReducer(reducer, {
        // default pagination
        page: 1,
        pageSize: 12,
        loading: false,
        scroll: true,

        showFilterDrawer: true,
        openFilterDrawer: true,

        variants: [],
        total: 0,

        filters: null,
        conditionFilter: [],
        modelFilter: [],
        rangeMonthlyPaymentFilter: [],
    });

const Context = createContext<CarListingContext | null>(null);

export const useCarListingState = () => {
    const context = useContext(Context);

    if (!context) {
        throw new Error('useCarListingState must be used within a CarListingStateProvider');
    }

    return context;
};

const stringToRange = (range: string) => {
    const [fromStr, toStr] = range.split('-');

    return {
        from: parseFloat(fromStr),
        to: parseFloat(toStr),
    };
};

const showFilter = <T extends {}>(filters: T[]) => filters && uniq(filters)?.filter(Boolean).length > 1;

const showFilterDrawer = (filters: FinderVehicleFilters) => {
    if (!filters) {
        return false;
    }

    return [filters.conditions, filters.modelCategories, filters.monthlyInstalments].some(showFilter);
};

type CarListingStateProviderProps = {
    endpoint: FinderApplicationEntrypointContextDataFragment;
    children: JSX.Element | React.ReactNode;
};

const CarListingStateProvider = ({ endpoint, children }: CarListingStateProviderProps) => {
    const [state, dispatch] = useFinderVehicleListReducer();
    const company = useCompany(true);
    const { dealerId } = useSingleDealerId();
    const { lead } = useFinderLeadContext();

    const previousList = useRef<FinderVehicleSelectionDataFragment[]>([]);

    const actions = useMemo(
        (): ActionMethods => ({
            setPage: page => dispatch({ type: 'setPage', page }),
            setPageSize: pageSize => dispatch({ type: 'setPageSize', pageSize }),
            setScroll: scroll => dispatch({ type: 'setScroll', scroll }),
            setOpenFilterDrawer: openFilterDrawer => dispatch({ type: 'setOpenFilterDrawer', openFilterDrawer }),
            setConditionFilter: conditionFilter => dispatch({ type: 'setConditionFilter', conditionFilter }),
            setModelFilter: modelFilter => dispatch({ type: 'setModelFilter', modelFilter }),
            setRangeMonthlyPaymentFilter: rangeMonthlyPaymentFilter =>
                dispatch({ type: 'setRangeMonthlyPaymentFilter', rangeMonthlyPaymentFilter }),
        }),
        [dispatch]
    );

    useFinderLeadFilters(actions);

    const conditions = useMemo(() => {
        const availableConditions = uniq(
            endpoint.finderApplicationModules.flatMap(({ finderVehicleConditions }) => finderVehicleConditions)
        );

        if (availableConditions.length > 1) {
            return state.conditionFilter?.map(condition => FinderVehicleCondition[condition]);
        }

        return availableConditions;
    }, [endpoint.finderApplicationModules, state.conditionFilter]);

    // reset page on dealer changed
    useEffect(() => {
        dispatch({ type: 'setPage', page: 1 });
    }, [dealerId, dispatch]);

    // get partner number when single dealer is selected
    const { dealersFromApi } = useDealerContext();
    const partnerNumber = useMemo(() => {
        const dealer = dealersFromApi.find(({ id }) => id === dealerId);
        if (isNil(dealer)) {
            return '';
        }

        return dealer.integrationDetails.partnerNumber || 'UNKNOWN';
    }, [dealerId, dealersFromApi]);

    const { data, loading } = useListFinderVehiclesForSelectionQuery({
        nextFetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyId: company.id,
                partnerNumber,
                // constraint, modules need to have same vehicle module id
                moduleId: endpoint.finderApplicationModules[0].vehicleModuleId,
                statuses: [FinderVehicleStatus.Available],
                conditions,
                modelCategories: state.modelFilter,
                rangeMonthlyInstalments: state.rangeMonthlyPaymentFilter?.length
                    ? state.rangeMonthlyPaymentFilter?.map(stringToRange)
                    : null,
                purpose: Purpose.Production,
            },
            pagination: { offset: (state.page - 1) * state.pageSize, limit: state.pageSize },
            sort: {
                field: FinderVehicleSortingField.InventoryId,
                order: SortingOrder.Asc,
            },
            // constraint, there will be only one financing module
            bankModuleId: endpoint.finderApplicationModules[0].bankModuleId,
            applicationModuleIds: endpoint.finderApplicationModules.map(module => module.id),
            dealerId: lead?.dealerId ?? dealerId,
        },
    });

    useEffect(() => {
        if (!state.scroll) {
            dispatch({ type: 'setVariants', variants: [] });
            previousList.current = [];
        }
    }, [dispatch, state.scroll]);

    useEffect(() => {
        dispatch({ type: 'setLoading', loading });

        if (data?.vehicles?.items && !loading) {
            previousList.current = uniqBy(
                'id',
                concat(state.page === 1 ? [] : previousList.current, data?.vehicles?.items)
            );

            dispatch({ type: 'setVariants', variants: previousList.current });
            if (!state.scroll) {
                dispatch({ type: 'setScroll', scroll: true });
            }
        }

        if (isFinite(data?.vehicles?.count)) {
            dispatch({ type: 'setTotal', total: data.vehicles.count });
        }
    }, [data, dispatch, loading, state.scroll]);

    useEffect(() => {
        if (data?.vehicles?.filters) {
            dispatch({
                type: 'setFilters',
                filters: data?.vehicles?.filters,
            });

            dispatch({
                type: 'setShowFilterDrawer',
                showFilterDrawer: showFilterDrawer(data?.vehicles?.filters),
            });
        }
    }, [data?.vehicles?.filters, state.filters, dispatch]);

    // Reset selected filters if no longer valid
    useEffect(() => {
        if (
            data?.vehicles?.filters &&
            state.rangeMonthlyPaymentFilter &&
            state.rangeMonthlyPaymentFilter.some(
                filter => !data?.vehicles?.filters?.monthlyInstalments?.map(rangeToString).includes(filter)
            )
        ) {
            actions.setRangeMonthlyPaymentFilter([]);
        }
    }, [data?.vehicles?.filters]);

    useEffect(() => {
        // if the visible height is less than 1440, default pageSize will be used.
        if (window.innerHeight >= 1440) {
            dispatch({ type: 'setPageSize', pageSize: window.innerHeight < 2200 ? 24 : 33 });
        }
    }, []);

    const context = useMemo(
        (): CarListingContext => ({ state, actions, meta: { endpoint, leadId: lead?.id } }),
        [state, actions, endpoint, lead?.id]
    );

    return (
        <Context.Provider value={context}>
            {state.scroll && loading && state.variants.length === 0 ? <PortalLoadingElement /> : children}
        </Context.Provider>
    );
};

export default CarListingStateProvider;
