import type {
    FinderApplicationEntrypointContextDataFragment,
    FinderVehicleSelectionDataFragment,
} from '../../../../api/fragments';
import { type FinderVehicleFilters } from '../../../../api/types';
import { PageState } from '../../MobilityApplicationEntrypoint/CarListingPage/CarListingStateProvider';

type State = PageState & {
    loading: boolean;

    /* Open or Close filter drawer. will has no effect if [showFilterDrawer] is false. */
    openFilterDrawer: boolean;

    /* Controls filter drawer's visibility */
    showFilterDrawer: boolean;

    total: number;
    scroll: boolean;
    variants: FinderVehicleSelectionDataFragment[];
    filters: FinderVehicleFilters;
    conditionFilter: string[];
    modelFilter: string[];
    rangeMonthlyPaymentFilter: string[];
};

export type ActionMethods = {
    setPage: (page: number) => void;
    setPageSize: (pageSize: number) => void;
    setScroll: (scroll: boolean) => void;
    setOpenFilterDrawer: (openFilterDrawer: boolean) => void;
    setConditionFilter: (conditionFilter: string[]) => void;
    setModelFilter: (modelFilter: string[]) => void;
    setRangeMonthlyPaymentFilter: (rangeMonthlyPaymentFilter: string[]) => void;
};

type Meta = {
    endpoint: FinderApplicationEntrypointContextDataFragment;
    leadId?: string;
};

export type CarListingContext = {
    state: State;
    actions: ActionMethods;
    meta: Meta;
};
