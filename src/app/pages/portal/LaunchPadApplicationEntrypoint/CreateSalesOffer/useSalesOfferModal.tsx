import { useMemo, useState } from 'react';
import SalesOfferModal from './SalesOfferModal';

type SalesOfferModalProps = {
    salesOfferModuleId: string;
    leadSuiteId: string;
    refetchLead?: () => void;
};

export const useSalesOfferModal = (props: SalesOfferModalProps) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(() => ({ open: () => setVisible(true), close: () => setVisible(false) }), [setVisible]);

    return useMemo(
        () => ({
            ...actions,
            render: () => <SalesOfferModal onClose={actions.close} visible={visible} {...props} />,
        }),
        [actions, props, visible]
    );
};

export default useSalesOfferModal;
