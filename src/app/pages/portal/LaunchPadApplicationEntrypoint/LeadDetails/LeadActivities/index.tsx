import { PText } from '@porsche-design-system/components-react';
import dayjs from 'dayjs';
import type { TFunction } from 'i18next';
import { getOr, isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import type { ReferenceApplicationDataFragment } from '../../../../../api/fragments/ReferenceApplicationData';
import {
    ApplicationScenario,
    ApplicationStage,
    ApplicationStatus,
    LeadStageOption,
    LeadStatus,
} from '../../../../../api/types';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import { useThemeComponents } from '../../../../../themes/hooks';
import { getApplicationStatus } from '../../../../../utilities/application';
import { getOffset } from '../../../../../utilities/date';
import useFormatDate from '../../../../../utilities/useFormatDate';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
// eslint-disable-next-line max-len
import type { ReferencePathPrefixes } from '../../../../shared/ApplicationDetailsPage/standard/ApplicationTab/ReferenceApplicationField';
import { ContentItem, ContentItemTitle } from '../../ui/ContentItem';
import { useLeadDetailsContext } from '../LeadDetailsContext';
import { ActivityColumns, type ActivityProps, renderStatusTag } from './helpers';
import useListReducer from './useListReducer';

type MinimumReferenceApplication = ReferenceApplicationDataFragment;

type ReferencePath<T extends MinimumReferenceApplication> = keyof Pick<
    T,
    | 'financingStage'
    | 'insuranceStage'
    | 'appointmentStage'
    | 'reservationStage'
    | 'mobilityStage'
    | 'tradeInStage'
    | 'visitAppointmentStage'
>;

type ReferenceApplicationItemProps = {
    stage?: ApplicationStage | 'Contact';
    application: MinimumReferenceApplication & {
        referenceApplications?: MinimumReferenceApplication[];
    };
};

type ReferenceItem = {
    title: string;
    path: string;
    stage: ApplicationStage | 'Contact';
    identifier: string;
    application: MinimumReferenceApplication;
};

type ReferenceLink = {
    [key in ApplicationStage]?: ReferenceItem;
} & {
    Contact?: ReferenceItem;
};

class ReferenceLinkGenerator {
    t: TFunction;

    _links: ReferenceLink = {};

    _currentStage?: ApplicationStage | 'Contact';

    _paths: ReferencePathPrefixes;

    constructor(t: TFunction, paths: ReferencePathPrefixes, currentStage?: ApplicationStage | 'Contact') {
        this._links = {};
        this._currentStage = currentStage;
        this._paths = paths;

        this.t = t;
    }

    getTitle(stage: ApplicationStage | 'Contact') {
        const { t } = this;

        const titleMap: Record<ApplicationStage & { Contact: string }, string> = {
            [ApplicationStage.Mobility]: t('customerDetails:steps.stage.mobilityBooking'),
            [ApplicationStage.Lead]: t('customerDetails:steps.stage.lead'),
            Contact: t('customerDetails:steps.stage.contact'),
            [ApplicationStage.Reservation]: t('customerDetails:steps.stage.reservation'),
            [ApplicationStage.Appointment]: t('customerDetails:steps.stage.appointment'),
            [ApplicationStage.VisitAppointment]: t('customerDetails:steps.stage.visitAppointment'),
            [ApplicationStage.Financing]: t('customerDetails:steps.stage.financing'),
            [ApplicationStage.Insurance]: t('customerDetails:steps.stage.insurance'),
            [ApplicationStage.TradeIn]: t('customerDetails:steps.stage.tradeIn'),
        };

        return titleMap[stage];
    }

    getPath(checkStage: ApplicationStage, id: string) {
        return this._paths?.stages[checkStage] ? `${this._paths.stages[checkStage]}/${id}` : null;
    }

    getLeadPath<T extends ReferenceApplicationItemProps['application']>(checkedApplication: T) {
        const { lead } = checkedApplication;

        if (lead.isLead) {
            return this._paths?.stages.Lead ? `${this._paths.stages.Lead}/${lead.versioning.suiteId}` : null;
        }

        return this._paths?.stages.Contact ? `${this._paths.stages.Contact}/${lead.versioning.suiteId}` : null;
    }

    canAddLink<T extends ReferenceApplicationItemProps['application']>(
        checkedApplication: T,
        path: ReferencePath<T>,
        checkStage: ApplicationStage
    ) {
        const checkedApplicationStage = getOr(null, path, checkedApplication);

        return checkedApplicationStage && !this._links[checkStage] && checkedApplication.stages?.includes?.(checkStage);
    }

    addStage<T extends ReferenceApplicationItemProps['application']>(
        checkedApplication: T,
        path: ReferencePath<T>,
        checkStage: ApplicationStage
    ) {
        if (this.canAddLink(checkedApplication, path, checkStage)) {
            this._links[checkStage] = {
                title: this.getTitle(checkStage),
                stage: checkStage,
                path: this.getPath(checkStage, checkedApplication.versioning.suiteId),
                identifier: checkedApplication[path].identifier,
                application: checkedApplication,
            };
        }
    }

    getLinks() {
        return this._links;
    }
}

// get application step related data
const getApplicationStepRelatedData = (
    application: MinimumReferenceApplication,
    applicationStage: ApplicationStage | 'Contact'
) => {
    const date = application?.versioning?.createdAt;

    let status: ApplicationStatus | LeadStatus = getApplicationStatus(
        application,
        applicationStage as ApplicationStage
    );
    let identifier = null;
    let isLead = false;

    switch (applicationStage) {
        case ApplicationStage.Mobility:
            if (
                application.__typename !== 'MobilityApplication' ||
                !application?.scenarios ||
                !application?.scenarios.includes(ApplicationScenario.Payment)
            ) {
                break;
            }

            identifier = application?.mobilityStage?.identifier;
            break;

        case ApplicationStage.Lead:
            identifier = application?.lead?.identifier;
            status = application.lead?.status;
            isLead = true;
            break;

        case 'Contact':
            identifier = application?.lead?.identifier;
            status = application.lead?.status;
            break;

        case ApplicationStage.Appointment:
            if (
                application.__typename !== 'ConfiguratorApplication' &&
                application.__typename !== 'FinderApplication' &&
                application.__typename !== 'StandardApplication' &&
                application.__typename !== 'EventApplication' &&
                application.__typename !== 'LaunchpadApplication'
            ) {
                break;
            }

            identifier = application?.appointmentStage.identifier;
            break;

        case ApplicationStage.VisitAppointment:
            if (application.__typename === 'EventApplication' || application.__typename === 'LaunchpadApplication') {
                identifier = application?.visitAppointmentStage.identifier;
            }

            break;

        case ApplicationStage.Insurance:
            if (
                application.__typename !== 'ConfiguratorApplication' &&
                application.__typename !== 'FinderApplication' &&
                application.__typename !== 'StandardApplication'
            ) {
                break;
            }

            identifier = application?.insuranceStage?.identifier;
            break;

        case ApplicationStage.Reservation:
            if (
                application.__typename !== 'ConfiguratorApplication' &&
                application.__typename !== 'FinderApplication' &&
                application.__typename !== 'StandardApplication' &&
                application.__typename !== 'EventApplication'
            ) {
                break;
            }

            identifier = application?.reservationStage?.identifier;
            break;

        case ApplicationStage.Financing:
            if (
                application.__typename !== 'ConfiguratorApplication' &&
                application.__typename !== 'FinderApplication' &&
                application.__typename !== 'StandardApplication'
            ) {
                break;
            }

            identifier = application?.financingStage?.identifier;
            break;

        case ApplicationStage.TradeIn:
        default:
            break;
    }

    return {
        status,
        identifier,
        date,
        vehicleName: application?.vehicle?.name,
        isLead,
    };
};

const LeadActivities = () => {
    const { t } = useTranslation(['launchpadLeadDetails', 'leadActivityList', 'customerDetails', 'applicationList']);
    const { lead } = useLeadDetailsContext();
    const { PaginatedTableWithContext } = useThemeComponents();
    const translatedString = useTranslatedString();
    const router = useRouter(true);
    const company = useCompany(true);
    const navigate = useNavigate();
    const { pathname } = useLocation();
    const formatDate = useFormatDate();

    const [state, dispatch] = useListReducer();
    const { page, pageSize } = state;

    // get endpoints
    const [
        eventEndpoint,
        leadEndpoint,
        contactEndpoint,
        appointmentEndpoint,
        visitAppointmentEndpoint,
        financingEndpoint,
        insuranceEndpoint,
        reservationEndpoint,
    ] = useMemo(() => {
        const prefix = router.endpoints.find(
            item =>
                item.__typename === 'EventApplicationEntrypoint' && item.eventApplicationModule?.id === lead.moduleId
        )?.pathname;

        const eventEndpoint = prefix ? ({ id }) => `${prefix}/events/${id}` : undefined;

        const leadEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'LeadListEndpoint' &&
                [LeadStageOption.Lead, LeadStageOption.LeadAndContact].includes(item.leadStage)
        )?.pathname;

        const contactEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'LeadListEndpoint' &&
                [LeadStageOption.Contact, LeadStageOption.LeadAndContact].includes(item.leadStage)
        )?.pathname;

        const appointmentEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'ApplicationListEndpoint' && item.applicationStage === ApplicationStage.Appointment
        )?.pathname;

        const visitAppointmentEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'ApplicationListEndpoint' &&
                item.applicationStage === ApplicationStage.VisitAppointment
        )?.pathname;

        const financingEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'ApplicationListEndpoint' && item.applicationStage === ApplicationStage.Financing
        )?.pathname;

        const insuranceEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'ApplicationListEndpoint' && item.applicationStage === ApplicationStage.Insurance
        )?.pathname;

        const reservationEndpoint = router?.endpoints?.find(
            item =>
                item.__typename === 'ApplicationListEndpoint' && item.applicationStage === ApplicationStage.Reservation
        )?.pathname;

        return [
            eventEndpoint,
            leadEndpoint,
            contactEndpoint,
            appointmentEndpoint,
            visitAppointmentEndpoint,
            financingEndpoint,
            insuranceEndpoint,
            reservationEndpoint,
        ];
    }, [router?.endpoints, lead?.moduleId]);

    // get reference paths
    const referencePaths = useMemo(
        () => ({
            stages: {
                [ApplicationStage.Lead]: leadEndpoint,
                Contact: contactEndpoint,
                [ApplicationStage.Appointment]: appointmentEndpoint,
                [ApplicationStage.VisitAppointment]: visitAppointmentEndpoint,
                [ApplicationStage.Financing]: financingEndpoint,
                [ApplicationStage.Insurance]: insuranceEndpoint,
                [ApplicationStage.Reservation]: reservationEndpoint,
            },
            references: {
                getEventDetail: eventEndpoint,
            },
        }),
        [
            leadEndpoint,
            contactEndpoint,
            appointmentEndpoint,
            visitAppointmentEndpoint,
            financingEndpoint,
            insuranceEndpoint,
            reservationEndpoint,
            eventEndpoint,
        ]
    );

    // get application step items
    const getApplicationStepItems = useCallback(
        (application: ReferenceApplicationItemProps['application']) => {
            const linkGenerator = new ReferenceLinkGenerator(t, referencePaths);

            linkGenerator.addStage(application, 'financingStage', ApplicationStage.Financing);
            linkGenerator.addStage(application, 'insuranceStage', ApplicationStage.Insurance);
            linkGenerator.addStage(application, 'appointmentStage', ApplicationStage.Appointment);
            linkGenerator.addStage(application, 'visitAppointmentStage', ApplicationStage.VisitAppointment);
            linkGenerator.addStage(application, 'reservationStage', ApplicationStage.Reservation);
            linkGenerator.addStage(application, 'mobilityStage', ApplicationStage.Mobility);
            linkGenerator.addStage(application, 'tradeInStage', ApplicationStage.TradeIn);

            // Reference applications is the one with apply for financing / insurance
            if (application?.referenceApplications?.length > 0) {
                application.referenceApplications.forEach(referenceApplication => {
                    linkGenerator.addStage(referenceApplication, 'financingStage', ApplicationStage.Financing);
                    linkGenerator.addStage(referenceApplication, 'insuranceStage', ApplicationStage.Insurance);
                    linkGenerator.addStage(referenceApplication, 'appointmentStage', ApplicationStage.Appointment);
                    linkGenerator.addStage(
                        referenceApplication,
                        'visitAppointmentStage',
                        ApplicationStage.VisitAppointment
                    );
                    linkGenerator.addStage(referenceApplication, 'reservationStage', ApplicationStage.Reservation);
                    linkGenerator.addStage(referenceApplication, 'mobilityStage', ApplicationStage.Mobility);
                    linkGenerator.addStage(referenceApplication, 'tradeInStage', ApplicationStage.TradeIn);
                });
            }

            const links = linkGenerator.getLinks();

            // Order it based on translation file
            const orderedLinks = [
                links[ApplicationStage.Mobility],
                links[ApplicationStage.Reservation],
                links[ApplicationStage.Appointment],
                links[ApplicationStage.VisitAppointment],
                links[ApplicationStage.Financing],
                links[ApplicationStage.Insurance],
                links[ApplicationStage.TradeIn],
            ].filter(Boolean);

            return orderedLinks.map(link => {
                const applicationStep: ActivityProps = {
                    title: link.title,
                    linkTo:
                        link.stage === ApplicationStage.TradeIn
                            ? `tradeIn/${link.application.versioning.suiteId}`
                            : link.path,
                    ...getApplicationStepRelatedData(link.application, link.stage),
                };

                return applicationStep;
            });
        },
        [t, referencePaths]
    );

    const additionalLeadAndContactRecords = useMemo(
        () => [
            {
                date: lead?.__typename === 'LaunchpadLead' ? lead?.qualifiedDate : lead?.versioning?.createdAt,
                title: t('customerDetails:steps.stage.lead'),
                identifier: lead?.identifier,
                vehicleName: lead?.vehicle?.name,
                status: lead?.status,
                isLead: true,
                linkTo: `${leadEndpoint}/${lead?.versioning?.suiteId}`,
            },
            {
                date: lead?.__typename === 'LaunchpadLead' ? lead?.contactedDate : lead?.versioning?.createdAt,
                title: t('customerDetails:steps.stage.contact'),
                identifier: null,
                vehicleName: lead?.vehicle?.name,
                status: ApplicationStatus.Contacted,
                isLead: false,
                linkTo: `${contactEndpoint}/${lead?.versioning?.suiteId}`,
            },
        ],
        [contactEndpoint, lead, leadEndpoint, t]
    );

    // Get activities
    const activities = useMemo(() => {
        let arrActivities = [];

        if (lead?.applications?.length > 0) {
            let activityList: ActivityProps[] = [];

            lead.applications.forEach(application => {
                const stepItems = getApplicationStepItems(application);
                activityList = [
                    ...activityList,
                    ...stepItems // only get 1 Lead / Contact from all application steps
                        .filter(
                            obj2 =>
                                !activityList.some(
                                    obj1 => obj1.title === obj2.title && obj1.identifier === obj2.identifier
                                )
                        )
                        .reverse(), // reverse to display latest at the top
                ];
            });

            // Sort by date descending
            arrActivities = activityList
                .sort((a, b) => dayjs(b.date).unix() - dayjs(a.date).unix())
                .map((activity, index) => ({ ...activity, id: index + 1, key: index + 1 }));
        }

        return [...arrActivities, ...additionalLeadAndContactRecords];
    }, [additionalLeadAndContactRecords, getApplicationStepItems, lead.applications]);

    const columns = useMemo(
        () => [
            {
                key: ActivityColumns.Date,
                dataIndex: ['date'],
                render: value =>
                    !isNil(value)
                        ? formatDate({
                              date: value,
                              timeZone: company?.timeZone,
                          })
                        : '',
                title: t('leadActivityList:columns.date', {
                    timezone: getOffset(t, company.timeZone),
                }),
            },
            {
                key: ActivityColumns.Activity,
                dataIndex: ['title'],
                title: t('leadActivityList:columns.activity'),
            },
            {
                key: ActivityColumns.AppId,
                dataIndex: ['identifier'],
                title: t('leadActivityList:columns.appId'),
            },
            {
                key: ActivityColumns.Vehicle,
                dataIndex: ['vehicleName'],
                render: (_, record) => translatedString(record.vehicleName),
                title: t('leadActivityList:columns.vehicle'),
            },
            {
                key: ActivityColumns.Status,
                dataIndex: ['status'],
                render: (_, record) => renderStatusTag(record, t),
                title: t('leadActivityList:columns.status'),
            },
        ],
        [company.timeZone, formatDate, t, translatedString]
    );

    const onRow = useCallback(
        (record: ActivityProps) => ({
            onClick: () => {
                navigate(record.linkTo, { state: { previousEndpoint: pathname } });
            },
        }),
        [navigate, pathname]
    );

    const dataSource = useMemo(() => {
        const skip = (page - 1) * pageSize;
        const take = pageSize;

        // client side pagination
        return activities.slice(skip, skip + take);
    }, [activities, page, pageSize]);

    const total = useMemo(() => activities.length, [activities]);

    return (
        <ContentItem>
            <ContentItemTitle>{t('leadActivityList:title')}</ContentItemTitle>
            {total > 0 ? (
                <PaginatedTableWithContext
                    columns={columns}
                    company={company}
                    dataSource={dataSource}
                    dispatch={dispatch}
                    onRow={onRow}
                    rowKey="id"
                    scroll={{ x: true }}
                    state={state}
                    tableName={t('leadActivityList:title')}
                    total={total}
                    alignPaginationCenter
                />
            ) : (
                <PText>{t('leadActivityList:noData.message')}</PText>
            )}
        </ContentItem>
    );
};

export default LeadActivities;
