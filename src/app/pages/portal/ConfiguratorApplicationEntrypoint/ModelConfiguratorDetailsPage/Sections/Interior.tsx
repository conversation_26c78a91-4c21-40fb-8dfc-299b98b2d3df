import { Grid } from 'antd';
import { useFormikContext } from 'formik';
import { head } from 'lodash/fp';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BlockDetailsFragment } from '../../../../../api/fragments/BlockDetails';
import { InventoryDetailsPublicDataFragment } from '../../../../../api/fragments/InventoryDetailsPublicData';
import { ModelConfiguratorDetailsFragment } from '../../../../../api/fragments/ModelConfiguratorDetails';
import { VariantConfiguratorDetailsFragment } from '../../../../../api/fragments/VariantConfiguratorDetails';
import { BlockType } from '../../../../../api/types';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import Media from '../components/Media';
import OptionBoxField from '../components/OptionBoxField';
import ProceedToSummary from '../components/ProceedToSummary';
import { SelectedSectionOption, ConfiguratorFormValues } from '../shared';
import { SectionTitle, ImageContainer, ColorDescription, SelectionContainer } from '../ui';
import { StyledPackageCombinationErrorMessage } from './Packages';
import getProceedToSummary from './getProceedToSummary';

export type InteriorProps = {
    variantConfigurator: VariantConfiguratorDetailsFragment;
    modelConfigurator: ModelConfiguratorDetailsFragment;
    block: BlockDetailsFragment;
    isLastBlock: boolean;
    setShowSummary: (value: boolean) => void;
    stockInventory?: InventoryDetailsPublicDataFragment[];
    setSelectedSection: (selectedSection: SelectedSectionOption) => void;
    filteredStockInventoryBySelectedSection: InventoryDetailsPublicDataFragment[];
    setHasError: (hasError: boolean) => void;
    hasError: boolean;
};

const Interior = ({
    variantConfigurator,
    modelConfigurator,
    block,
    isLastBlock,
    setShowSummary,
    stockInventory,
    setSelectedSection,
    setHasError,
    hasError,
    filteredStockInventoryBySelectedSection,
}: InteriorProps) => {
    const { t } = useTranslation('configuratorDetails');
    const { values, setFieldValue } = useFormikContext<ConfiguratorFormValues>();
    const screens = Grid.useBreakpoint();
    const isMobile = useMemo(() => !screens.lg, [screens]);
    const translatedString = useTranslatedString();

    const trimBlockInventoryList = stockInventory.filter(stock => stock.availableStock > 0);

    const variantTrimBlock = variantConfigurator.blocks.find(block => block.__typename === 'TrimBlock');
    if (variantTrimBlock.__typename !== 'TrimBlock') {
        throw new Error('Trim Block is missing.');
    }

    const { trimBlock, matrix } = useMemo(() => {
        const colorBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Color);
        const trimBlock = values.configuratorBlocks.find(block => block.blockType === BlockType.Trim);

        const matrix = variantConfigurator.matrix.find(
            matrix => matrix.colorId === head(colorBlock?.ids) && matrix.trimId === head(trimBlock?.ids)
        );

        return { trimBlock, matrix };
    }, [values.configuratorBlocks, variantConfigurator.matrix]);

    const onSelect = useCallback(
        (value: string) => {
            const latestBlock = [
                ...values.configuratorBlocks.filter(block => block.blockType !== BlockType.Trim),
                {
                    blockId: block.id,
                    blockType: BlockType.Trim,
                    ids: [value],
                    combo: [],
                },
            ];
            setFieldValue('configuratorBlocks', latestBlock);
            setSelectedSection(SelectedSectionOption.Trim);
        },
        [block.id, setFieldValue, setSelectedSection, values.configuratorBlocks]
    );

    if (block?.__typename !== 'TrimBlock') {
        return null;
    }

    const showError = useMemo(() => {
        if (hasError) {
            return !getProceedToSummary(stockInventory, values.configuratorBlocks);
        }

        return false;
    }, [hasError, stockInventory, values.configuratorBlocks]);

    return (
        <SelectionContainer>
            {isMobile && (
                <ImageContainer>
                    <Media fileName={matrix?.variantTrimImage?.filename} source={matrix?.variantTrimImage?.url} />
                </ImageContainer>
            )}
            <div className="configurator">
                <SectionTitle>{translatedString(block.sectionTitle)}</SectionTitle>
                <OptionBoxField
                    filteredStockInventoryBySelectedSection={filteredStockInventoryBySelectedSection}
                    inventoryList={trimBlockInventoryList}
                    onClick={onSelect}
                    options={block.trimSettings.map(trimSetting => ({
                        id: trimSetting.id,
                        image: trimSetting.image?.url,
                        color: trimSetting.hex,
                        name: trimSetting.code,
                    }))}
                    selectedId={head(trimBlock?.ids)}
                />
                <ColorDescription>
                    {translatedString(
                        block.trimSettings.find(trimSetting => trimSetting.id === head(trimBlock?.ids))?.name
                    )}
                </ColorDescription>
                {showError && (
                    <StyledPackageCombinationErrorMessage>
                        {t('configuratorDetails:messages.noCombinationConfiguratorVariant')}
                    </StyledPackageCombinationErrorMessage>
                )}
                {isLastBlock && (
                    <ProceedToSummary
                        modelConfigurator={modelConfigurator}
                        setHasError={setHasError}
                        setShowSummary={setShowSummary}
                        stockInventory={stockInventory}
                        variantConfigurator={variantConfigurator}
                    />
                )}
            </div>
        </SelectionContainer>
    );
};

export default Interior;
