import { LabelsPublicDataFragment } from '../../../../api/fragments/LabelsPublicData';
import { PromoCodeDataFragment } from '../../../../api/fragments/PromoCodeData';
import {
    ConfiguratorApplicationConfigurationPayload,
    ConfiguratorBlockPayload,
    OptionKind,
    DiscountType,
    BlockType,
} from '../../../../api/types';
import { GenericCalculatorValues } from '../../../../calculator/types';
import type { ConfiguratorApplicationState } from '../Journey/shared';

export enum SelectedSectionOption {
    Color = 'color',
    Trim = 'trim',
    Package = 'package',
}

export type ActionsProps = {
    setShowSummary: (showSummary: boolean) => void;
    setPromoCode: (promoCode: PromoCodeDataFragment | null) => void;
    setSaveAsDraft: (saveAsDraft: boolean) => void;
    setEmailSent: (emailSent: boolean) => void;
    setPromoCodeInput: (promoCodeInput: string) => void;
    setPromoCodeError: (promoCodeError: string) => void;
    setDealerId: (dealerId: string) => void;
    setSelectedSection: (selectedSection: SelectedSectionOption) => void;
    setHasError: (hasError: boolean) => void;
    setVariantConfigurator: (variantConfiguratorId: string) => void;
    setLabel: (label: LabelsPublicDataFragment[]) => void;
    setLabelDisplay: (labelDisplay: boolean) => void;
};

export type State = {
    showSummary: boolean;
    promoCodeInput: string;
    promoCodeError: string;
    promoCode: PromoCodeDataFragment | null;
    saveAsDraft: boolean;
    emailSent: boolean;
    dealerId: string;
    variantConfiguratorId: string;
    selectedSection: SelectedSectionOption;
    hasError: boolean;
    label: LabelsPublicDataFragment[] | null;
    labelDisplay: boolean;
    applyNewApplication?: ConfiguratorApplicationState | null;
};

export const promoCodePercentage = (promoCode: PromoCodeDataFragment, financedAmount: number) => {
    if (promoCode.promoType.__typename === 'DiscountPromoType') {
        if (promoCode.promoType.discountType === DiscountType.Percentage) {
            return promoCode.promoType.amount;
        }

        return (promoCode.promoType.amount / financedAmount) * 100;
    }

    return null;
};

export const promoCodeValue = (promoCode: PromoCodeDataFragment, financedAmount: number) => {
    if (promoCode.promoType.__typename === 'DiscountPromoType') {
        if (promoCode.promoType.discountType === DiscountType.Percentage) {
            const deductedAmount = (financedAmount * promoCode.promoType.amount) / 100;

            if (deductedAmount > financedAmount) {
                return financedAmount;
            }

            return deductedAmount;
        }

        if (promoCode.promoType.amount > financedAmount) {
            return financedAmount;
        }

        return promoCode.promoType.amount;
    }

    return null;
};

export type ConfiguratorBlock = ConfiguratorBlockPayload & { blockType: BlockType };
export type OptionFormValues =
    | { id: string; kind: OptionKind.SingleSelect; values: string }
    | { id: string; kind: OptionKind.MultiSelect; values: string[] }
    | { id: string; kind: OptionKind.Combo; values: { id: string; value: string }[] };
export type ConfiguratorFormValues = {
    calculator: GenericCalculatorValues;
    configuration: ConfiguratorApplicationConfigurationPayload;
    configuratorBlocks: ConfiguratorBlock[];
    configuratorId: string;
    options: OptionFormValues[];
    dealerId: string;
    email: string;
};
