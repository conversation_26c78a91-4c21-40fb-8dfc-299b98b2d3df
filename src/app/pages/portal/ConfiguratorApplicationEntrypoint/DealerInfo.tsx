import { PHeading, PText, PTextProps } from '@porsche-design-system/components-react';
import { Space, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { DealerJourneyDataFragment } from '../../../api/fragments/DealerJourneyData';
import { useRouter } from '../../../components/contexts/shared';
import renderMarkdown from '../../../utilities/renderMarkdown';
import useTranslatedString from '../../../utilities/useTranslatedString';

const DealerName = styled(Typography)`
    &.ant-typography {
        font-weight: 900;
        font-size: 20px;
    }
`;

const DealerContactAntd = styled(Typography)`
    &.ant-typography {
        font-size: 16px;
        color: #0e0e0e;
        white-space: pre-wrap;
    }
`;

const StyledSpace = styled(Space)`
    width: 100%;
`;

export type DealerInfoProps = {
    dealer: DealerJourneyDataFragment;
};

const DealerInfo = ({ dealer }: DealerInfoProps) => {
    const { t } = useTranslation('configuratorJourney');
    const translatedString = useTranslatedString();
    const { layout } = useRouter();
    const v3LayoutType = layout?.__typename === 'PorscheV3Layout';

    const DealerContact = v3LayoutType
        ? (props: PTextProps) => <PText {...props} style={{ marginBottom: '4px', ...(props.style || {}) }} />
        : DealerContactAntd;

    return (
        <StyledSpace direction="vertical" size={v3LayoutType ? 16 : 24}>
            <div>
                {v3LayoutType ? (
                    <PHeading size="medium" style={{ marginBottom: '16px' }}>
                        {translatedString(dealer.legalName)}
                    </PHeading>
                ) : (
                    <DealerName>{translatedString(dealer.legalName)}</DealerName>
                )}
                {dealer.contact.address?.defaultValue && !v3LayoutType && (
                    <DealerContact>{translatedString(dealer.contact.address)}</DealerContact>
                )}
            </div>
            {(dealer.contact.telephone || dealer.contact.email || dealer.contact.address) && (
                <div>
                    {dealer.contact.address?.defaultValue && v3LayoutType && (
                        <DealerContact>{translatedString(dealer.contact.address)}</DealerContact>
                    )}

                    {dealer.contact.telephone && (
                        <DealerContact>
                            {t('configuratorJourney:thankyou.contents.dealerInfo.phone', dealer.contact.telephone)}
                        </DealerContact>
                    )}

                    {dealer.contact.email && (
                        <DealerContact>
                            {t('configuratorJourney:thankyou.contents.dealerInfo.email', {
                                email: dealer.contact.email,
                            })}
                        </DealerContact>
                    )}
                </div>
            )}
            {dealer.contact.additionalInfo?.defaultValue && (
                <div>
                    <DealerContact>{renderMarkdown(translatedString(dealer.contact.additionalInfo))}</DealerContact>
                </div>
            )}
        </StyledSpace>
    );
};

export default DealerInfo;
