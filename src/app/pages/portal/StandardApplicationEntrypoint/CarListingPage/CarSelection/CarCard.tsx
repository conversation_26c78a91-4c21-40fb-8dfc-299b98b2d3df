import { CardProps } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { roundUpWithPrecision } from '../../../../../utilities/rounding';
import useCompanyFormats from '../../../../../utilities/useCompanyFormats';
import usePublic from '../../../../../utilities/usePublic';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import { StyledVehicleCard, StyledVehicleCardMeta } from '../../../../shared/CIPage/carListingUI';
import { Variants } from '../shared';

export type CarCardProps = {
    variant: Variants[number];
    onClick?: () => void;
} & CardProps;

const CarCard = ({ variant, onClick, ...props }: CarCardProps) => {
    const { t } = useTranslation('carList');
    const translate = useTranslatedString();
    const { formatAmountWithCurrency, amountDecimals } = useCompanyFormats();
    const fallbackSrc = usePublic('empty.svg');
    const image = useMemo(() => variant.images?.[0]?.url || fallbackSrc, [fallbackSrc, variant.images]);

    const title = translate(variant.name);

    const description = useMemo(() => {
        const base = formatAmountWithCurrency(variant.vehiclePrice);

        if (variant?.monthlyInstalment) {
            const amount = formatAmountWithCurrency(roundUpWithPrecision(variant.monthlyInstalment, amountDecimals));

            return `${base} ${t('carList:descriptions.monthlyInstalment', { amount })}`;
        }

        return base;
    }, [amountDecimals, formatAmountWithCurrency, t, variant?.monthlyInstalment, variant?.vehiclePrice]);

    return (
        <StyledVehicleCard
            key={variant.id}
            bordered={false}
            cover={<img alt={title} src={image} />}
            hoverable={false}
            onClick={onClick}
            {...props}
        >
            <StyledVehicleCardMeta description={description} title={title} />
        </StyledVehicleCard>
    );
};

export default CarCard;
