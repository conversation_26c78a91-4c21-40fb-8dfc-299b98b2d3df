// eslint-disable-next-line max-len
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
// eslint-disable-next-line max-len
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import { ListLocalVariantsForSelectionQuery } from '../../../../api/queries/listLocalVariantsForSelection';

export type Variants = ListLocalVariantsForSelectionQuery['list']['items'];

export type State = {
    variants: Variants;
    selectedModels: string[];
    selectedSubmodels: string[];
    selectedMonthlyPayments: string[];

    /* Open or Close filter drawer. will has no effect if [showFilterDrawer] is false. */
    openFilterDrawer: boolean;

    /* Controls filter drawer's visibility */
    showFilterDrawer: boolean;
};

export type ActionMethods = {
    setSelectedModels: (models: string[]) => void;
    setSelectedSubmodels: (submodels: string[]) => void;
    setSelectedMonthlyPayments: (monthlyPayments: string[]) => void;
    setOpenFilterDrawer: (open: boolean) => void;
};

export type Meta = {
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
    dealerIds: string[];
};

export type CarListingContext = {
    state: State;
    actions: ActionMethods;
    meta: Meta;
};
