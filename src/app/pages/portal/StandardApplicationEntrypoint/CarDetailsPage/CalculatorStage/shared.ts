import type { ConfigurationValues } from '../../../../../calculator/form/types';
import { GenericCalculatorValues } from '../../../../../calculator/types';

export type CalculatorFormValues = {
    calculator: GenericCalculatorValues;
    configuration: ConfigurationValues;
};

export type State = { configuration: ConfigurationValues };

export type ActionsProps = {
    setConfiguration: (configuration: ConfigurationValues) => void;
};
