/* eslint-disable max-len */
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import { StandardApplicationPublicAccessEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationPublicAccessEntrypointContextData';
import type { KYCJourneyValues } from '../KYCPage/shared';
import { CalculatorFormValues } from './CalculatorStage/shared';

export type CarDetailPageProps = {
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | StandardApplicationPublicAccessEntrypointContextDataFragment;
};

export type StandardJourneyTemporaryValue = {
    token: string;
    sessionId: string;
    expiresAt?: Date | string | null;
    draftCreated: boolean;
    calculatorValues: CalculatorFormValues;
    kyc?: KYCJourneyValues;
    withMyInfo?: boolean;
};
