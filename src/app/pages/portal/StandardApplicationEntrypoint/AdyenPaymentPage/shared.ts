import { ComponentType, Dispatch, PropsWithChildren } from 'react';
import { DefaultLayoutProps } from '../../../../themes/default/Standard/Layout';
import { AllowedApplicationForPayment } from '../../../../utilities/journeys/payment';
import type { Action, State } from '../Journey/shared';

export enum AgreementTypes {
    TextApplicationAgreement = 'TextApplicationAgreement',
    CheckboxApplicationAgreement = 'CheckboxApplicationAgreement',
    MarketingApplicationAgreement = 'MarketingApplicationAgreement',
}

export type AdyenPaymentPageProps = {
    state: State<AllowedApplicationForPayment>;
    dispatch: Dispatch<Action<AllowedApplicationForPayment>>;
    CustomLayout: ComponentType<PropsWithChildren<DefaultLayoutProps>>;
    promoCodeModuleId?: string;
    applicationModuleId?: string;
    giftVoucherModuleId?: string;
    shouldIncludeLayout?: boolean;
};
