import { DebugJourneyDataFragment } from '../../../../api/fragments/DebugJourneyData';

export type State<TApplicationState> = {
    stage: JourneyStage;
    token: string;
    application: TApplicationState;
    stages: JourneyStage[];
};

export type StandardApplicationState = Extract<
    DebugJourneyDataFragment['application'],
    { __typename: 'StandardApplication' }
>;

export type Action<TApplicationState> =
    | ({ type: 'refresh' | 'next' } & Omit<State<TApplicationState>, 'stage' | 'stages'>)
    | { type: 'goTo'; stage: JourneyStage };

export enum JourneyStage {
    /* we are not yet aware of which set is first so we let the system initialize it */
    Initialize = 'initialize',

    /* Applicant KYC done with sales person */
    ApplicantKYC = 'sales-applicant-kyc',

    /* Applicant KYC with Customer's device */
    CustomerApplicantKYC = 'customer-applicant-kyc',

    /* Guarantor KYC  */
    GuarantorKYC = 'guarantor-kyc',

    /* Guarantor KYC  */
    GuarantorAgreements = 'guarantor-agreements',

    /* Deposit */
    Deposit = 'deposit',

    /* Namirial */
    Namirial = 'namirial',

    /* Namirial redirect page */
    NamirialRedirect = 'namirialRedirect',

    /* Namirial Signing Reject Page */
    NamirialReject = 'namirialReject',

    /* Namirial Timeout Page */
    NamirialTimeout = 'namirialTimeout',

    /* Namirial */
    GuarantorNamirial = 'guarantorNamirial',

    /* Namirial redirect page */
    GuarantorNamirialRedirect = 'guarantorNamirialRedirect',

    /* Namirial Signing Reject Page */
    GuarantorNamirialReject = 'guarantorNamirialReject',

    /* Namirial Timeout Page */
    GuarantorNamirialTimeout = 'guarantorNamirialTimeout',

    /* Insurance Namirial */
    InsuranceNamirial = 'insuranceNamirial',

    /* Insurance namirial redirect page */
    InsuranceNamirialRedirect = 'insuranceNamirialRedirect',

    /* OTP */
    Otp = 'otp',

    /* Insurance OTP */
    InsuranceOtp = 'insuranceOtp',

    /* Confirm email send to custoemr page */
    ConfirmEmailSend = 'confirmEmailSend',

    /* Appointment  */
    Appointment = 'appointment',

    /* Test drive started  */
    TestDriveStarted = 'testDriveStarted',

    /* Error fallback */
    Unknown = 'unknown',

    /* Namirial */
    TestDriveNamirial = 'testDriveNamirial',

    /* Test Drive KYC */
    TestDriveKYC = 'testDriveKyc',

    /* Test Drive Agreement */
    TestDriveAgreement = 'testDriveAgreement',

    /* OTP */
    TestDriveOtp = 'testDriveOtp',

    /* Namirial redirect page */
    TestDriveNamirialRedirect = 'testDriveNamirialRedirect',

    /* Namirial Signing Reject Page */
    TestDriveNamirialReject = 'testDriveNamirialReject',

    /* Namirial Timeout Page */
    TestDriveNamirialTimeout = 'testDriveNamirialTimeout',

    /* KYC & Appointment */
    KYCAndAppointment = 'kycAndAppointment',

    /* Porsche ID Login / Register */
    PorscheIdLoginRegister = 'porscheIdLoginRegister',

    Confirmation = 'confirmation',
}
