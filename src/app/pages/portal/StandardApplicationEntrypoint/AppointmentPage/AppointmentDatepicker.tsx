/* eslint-disable max-len */
import { Col, ColProps, Row, RowProps, Typography } from 'antd';
import { useFormikContext } from 'formik';
import { get, isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import {
    AppointmentModuleSpecsFragment,
    JourneyEventDataFragment,
    VisitAppointmentModuleSpecsFragment,
} from '../../../../api';
import { AppointmentModuleApplicationJourneyFragment } from '../../../../api/fragments/AppointmentModuleApplicationJourney';
import CheckboxField from '../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../themes/hooks';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import type { AppointmentValues } from './appointmentValues';
import useAppointmentAvailability from './useAppointmentAvailability';

const Description = styled.div`
    margin-top: 0.75rem;
    font-size: 16px;
`;

type AppointmentDatepickerProps = {
    bookedAppointmentTimeSlots?: (string | Date)[];
    prefix?: string;
    rowGap?: RowProps['gutter'];
    colSpan?: Pick<ColProps, 'span' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'>;
    title?: React.ReactNode;
    hideBookingInformation?: boolean;
    helperText?: React.ReactNode;
    required?: boolean;
    showSkipValidation?: boolean;
    disabled?: boolean;
    showTitle?: boolean;
    event?: JourneyEventDataFragment;
    appointmentModule: AppointmentModuleSpecsFragment | VisitAppointmentModuleSpecsFragment;
};

const defaultColSpan = { span: 24 };
const defaultRowGap: RowProps['gutter'] = [24, 0];

type AppointmentDatepickerFieldsProps = Omit<
    AppointmentDatepickerProps,
    'prefix' | 'title' | 'hideBookingInformation' | 'helperText' | 'applicationModule'
> & {
    getFieldName: (inputName: string) => string;
    values: AppointmentValues;
    setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void;
    appointmentModule: AppointmentModuleApplicationJourneyFragment | VisitAppointmentModuleSpecsFragment;
};

const AppointmentDatepickerFieldDates = ({
    appointmentModule,
    bookedAppointmentTimeSlots,
    colSpan,
    required,
    getFieldName,
    values,
    setFieldValue,
    disabled,
}: Omit<AppointmentDatepickerFieldsProps, 'showSkipValidation' | 'rowGap'>) => {
    const { t } = useTranslation(['appointmentPage']);
    const { FormFields } = useThemeComponents();

    const { availableTimeSlots, isDisabledDate } = useAppointmentAvailability(
        appointmentModule,
        bookedAppointmentTimeSlots,
        values.date,
        appointmentModule.company.timeZone
    );

    useEffect(() => {
        if (isNil(values.date)) {
            setFieldValue(getFieldName('time'), null);
        }
    }, [values.date, getFieldName, setFieldValue]);

    return (
        <>
            <Col {...colSpan}>
                <FormFields.DatePickerField
                    {...t('appointmentPage:fields.date', { returnObjects: true })}
                    disabled={disabled}
                    disabledDate={isDisabledDate}
                    name={getFieldName('date')}
                    picker="date"
                    required={required}
                    style={{ width: '100%' }}
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.SelectField
                    disabled={!values.date || disabled}
                    label={t('appointmentPage:fields.time.label')}
                    name={getFieldName('time')}
                    options={availableTimeSlots}
                    required={required}
                    style={{ width: '100%' }}
                />
            </Col>
        </>
    );
};

const AppointmentDatepickerFields = ({
    appointmentModule,
    bookedAppointmentTimeSlots,
    colSpan,
    rowGap,
    required,
    showSkipValidation,
    getFieldName,
    values,
    setFieldValue,
    disabled,
}: AppointmentDatepickerFieldsProps) => {
    const { t } = useTranslation(['appointmentPage']);
    const { Checkbox } = useThemeComponents();

    return (
        <Row gutter={rowGap}>
            {showSkipValidation && (
                <Col {...colSpan}>
                    <CheckboxField
                        checked={values.useCurrentDateTime}
                        customComponent={Checkbox}
                        name={getFieldName('useCurrentDateTime')}
                    >
                        {t('appointmentPage:fields.currentDateTime.label')}
                    </CheckboxField>
                </Col>
            )}
            {!values.useCurrentDateTime && (
                <AppointmentDatepickerFieldDates
                    appointmentModule={appointmentModule}
                    bookedAppointmentTimeSlots={bookedAppointmentTimeSlots}
                    colSpan={colSpan}
                    disabled={disabled}
                    getFieldName={getFieldName}
                    required={required}
                    setFieldValue={setFieldValue}
                    values={values}
                />
            )}
        </Row>
    );
};

const AppointmentDatepicker = ({
    bookedAppointmentTimeSlots,
    prefix,
    colSpan = defaultColSpan,
    rowGap = defaultRowGap,
    title,
    hideBookingInformation,
    helperText,
    required,
    showSkipValidation,
    disabled,
    showTitle = true,
    event,
    appointmentModule,
}: AppointmentDatepickerProps) => {
    const { t } = useTranslation(['appointmentPage']);

    const translated = useTranslatedString();

    const { values, setFieldValue } = useFormikContext();

    const getFieldName = useCallback((inputName: string) => (prefix ? `${prefix}.${inputName}` : inputName), [prefix]);

    const appointmentValues = useMemo(
        (): AppointmentValues => ({
            date: get(getFieldName('date'), values),
            time: get(getFieldName('time'), values),
            useCurrentDateTime: get(getFieldName('useCurrentDateTime'), values),
        }),
        [getFieldName, values]
    );

    return (
        <>
            {showTitle
                ? (title ?? <Typography.Title level={4}>{t('appointmentPage:subTitle')}</Typography.Title>)
                : null}
            <AppointmentDatepickerFields
                appointmentModule={appointmentModule}
                bookedAppointmentTimeSlots={bookedAppointmentTimeSlots}
                colSpan={colSpan}
                disabled={disabled}
                getFieldName={getFieldName}
                required={required}
                rowGap={rowGap}
                setFieldValue={setFieldValue}
                showSkipValidation={showSkipValidation}
                values={appointmentValues}
            />
            {helperText}
            {!hideBookingInformation && <Description>{translated(appointmentModule.bookingInformation)}</Description>}
        </>
    );
};

export default AppointmentDatepicker;
