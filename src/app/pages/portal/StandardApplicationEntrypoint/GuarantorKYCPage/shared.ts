import { CustomerKind, StandardApplicationConfiguration, TradeInVehiclePayload } from '../../../../api/types';

import { KYCPresetFormFields } from '../../../../utilities/kycPresets';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';

export type ApplicantFormValues = {
    fields: KYCPresetFormFields;
};

export type GuarantorKYCJourneyValues = {
    agreements: AgreementValues;
    customer: ApplicantFormValues;
    configuration: StandardApplicationConfiguration;
    tradeInVehicle: TradeInVehiclePayload[];
    hasGuarantor: boolean;
    prefix?: string;
    vsoUpload: File[];
    uploadDocuments?: {
        [CustomerKind.Guarantor]?: File[];
    };
    prefill: boolean;
};
