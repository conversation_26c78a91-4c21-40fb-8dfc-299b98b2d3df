/* eslint-disable max-len */
import { Dispatch } from 'react';
import {
    ConfiguratorApplicationEntrypointContextDataFragment,
    EventApplicationEntrypointContextDataFragment,
    FinderApplicationEntrypointContextDataFragment,
    FinderApplicationPublicAccessEntrypointContextDataFragment,
} from '../../../../api/fragments';
import { StandardApplicationEntrypointContextDataFragment } from '../../../../api/fragments/StandardApplicationEntrypointContextData';
import { CustomerKind, TradeInVehiclePayload } from '../../../../api/types';
import { KYCPresetFormFields } from '../../../../utilities/kycPresets';
import { AgreementValues } from '../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import type { Action, State } from '../../StandardApplicationEntrypoint/Journey/shared';
import type { TestDriveJourneyState } from '../Journey/shared';

export type ApplicantFormValues = {
    fields: KYCPresetFormFields;
};

export type TestDriveKYCJourneyValues = {
    agreements: AgreementValues;
    customer: ApplicantFormValues;
    tradeInVehicle?: TradeInVehiclePayload[];
    prefix?: string;
    vsoUpload?: File[];
    uploadDocuments?: {
        [CustomerKind.Local]?: File[];
        [CustomerKind.Corporate]?: File[];
    };
    prefill: boolean;
};

export type TestDriveKYCPageProps = {
    state: State<TestDriveJourneyState>;
    dispatch: Dispatch<Action<TestDriveJourneyState>>;
    endpoint:
        | StandardApplicationEntrypointContextDataFragment
        | FinderApplicationEntrypointContextDataFragment
        | EventApplicationEntrypointContextDataFragment
        | ConfiguratorApplicationEntrypointContextDataFragment
        | FinderApplicationPublicAccessEntrypointContextDataFragment;
};
