import type { ConfiguratorApplicationState } from '../../ConfiguratorApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../../EventApplicationEntrypoint/Journey/shared';
import type { FinderApplicationState } from '../../FinderApplicationPublicAccessEntrypoint/shared';
import { LaunchpadApplicationState } from '../../LaunchPadApplicationEntrypoint/utils/types';
import { type StandardApplicationState } from '../../StandardApplicationEntrypoint/Journey/shared';

export type TestDriveJourneyState =
    | ConfiguratorApplicationState
    | EventApplicationState
    | StandardApplicationState
    | FinderApplicationState
    | LaunchpadApplicationState;
