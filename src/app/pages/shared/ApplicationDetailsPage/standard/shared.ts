import { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';
import { CustomerSpecsFragment } from '../../../../api/fragments/CustomerSpecs';
import { GenericCalculatorValues } from '../../../../calculator/types';
import { KYCPresetFormFields } from '../../../../utilities/kycPresets';
import { AgreementValues } from '../../CIPage/ConsentAndDeclarations/useAgreementsValues';
import { QuotationFormValues } from '../../JourneyPage/QuotationDetails/types';

export type AFCApplicationFormValues = Omit<
    Extract<
        ApplicationDataFragment,
        {
            __typename:
                | 'StandardApplication'
                | 'ConfiguratorApplication'
                | 'EventApplication'
                | 'FinderApplication'
                | 'LaunchpadApplication';
        }
    >,
    | 'availableAssignees'
    | 'documents'
    | 'endpoint'
    | 'financing'
    | 'applicant'
    | 'insurancing'
    | 'guarantor'
    | 'quotation'
> &
    QuotationFormValues & {
        financing: GenericCalculatorValues;
        // we rename it in admin from `applicant` to `customer` so that we can reuse kyc components from journey kyc
        customer: Omit<CustomerSpecsFragment, 'fields'> & { fields: KYCPresetFormFields };
        guarantor?: Omit<CustomerSpecsFragment, 'fields'> & { fields: KYCPresetFormFields };
        withFinancing: boolean;
        withInsurance: boolean;
        tradeIn?: boolean;
        agreements: AgreementValues;
        guarantorAgreementValues?: AgreementValues;
        insurancing?: Partial<GenericCalculatorValues>;
    };

export type ApplicationFormValues = AFCApplicationFormValues & { commentsToInsurer?: string };

export const isApplicationRequestForFinancing = (application: ApplicationDataFragment | null | undefined) => {
    if (!application) {
        return null;
    }

    switch (application.__typename) {
        case 'ConfiguratorApplication':
        case 'FinderApplication':
            return application.configuration.requestForFinancing && !application.configuration.withFinancing;

        default:
            return false;
    }
};

export enum ActionKey {
    Void = 'void',
    Complete = 'complete',
    Approve = 'approve',
    Decline = 'decline',
    RequestReleaseLetter = 'requestReleaseLetter',
    RequestDisbursement = 'requestDisbursement',
    DownloadAllDocuments = 'downloadAllDocuments',
    RefreshApplicationStatus = 'refreshApplicationStatus ',
    Checkin = 'checkin',
    Contact = 'contact',
    AgreementConcluded = 'agreementConcluded',
    ConfirmBooking = 'confirmBooking',
    StartTestDrive = 'startTestDrive',
    EndTestDrive = 'endTestDrive',
    ConvertToApplication = 'convertToApplication',
    QualifyContact = 'qualifyContact',
    UnqualifyContact = 'unqualifyContact',
    ResubmitToCap = 'resubmitToCap',
    ApplyNew = 'applyNew',
    CompleteLead = 'completeLead',
    MarkLeadAsLost = 'markLeadAsLost',
}
