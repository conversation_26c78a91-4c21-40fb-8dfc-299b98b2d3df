import dayjs from 'dayjs';
import { ApplicationDataFragment, CustomerSpecsFragment } from '../../../api/fragments';
import { PeriodPayload } from '../../../api/types';
import { KYCPresetFormFields } from '../../../utilities/kycPresets';
import { AgreementValues } from '../CIPage/ConsentAndDeclarations/useAgreementsValues';

export type MobilityBookingFormValues = Omit<
    Extract<ApplicationDataFragment, { __typename: 'MobilityApplication' }>,
    'guarantor'
> & {
    agreements: AgreementValues;
    customer: Omit<CustomerSpecsFragment, 'fields'> & { fields: KYCPresetFormFields };
    guarantor?: Omit<CustomerSpecsFragment, 'fields'> & { fields: KYCPresetFormFields };
    rentalBookingPeriod: PeriodPayload;
    startTime: dayjs.Dayjs | null;
    endTime: dayjs.Dayjs | null;

    /** The values is in `MobilityBookingLocationPayload`, but parsed as string */
    location?: string;
};

export type MobilityBookingFormProps = {
    application: Extract<ApplicationDataFragment, { __typename: 'MobilityApplication' }>;
};
