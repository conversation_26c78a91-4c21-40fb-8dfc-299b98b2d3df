import { createContext, useContext } from 'react';
import { RouterContextDataFragment } from '../../api/fragments/RouterContextData';
import { Maybe } from '../../api/types';
import type { ContentRefinementSourceType } from '../../api/types';
import type { RouteDefinition } from '../../utilities/shared';
/* eslint-enable no-redeclare */

export type RouterContextPayload = RouterContextDataFragment & { refetch: () => Promise<unknown> };

export const RouterContext = createContext<RouterContextPayload | null>(null);

/* eslint-disable no-redeclare */
export function useRouter(required: true): RouterContextPayload;
export function useRouter(required?: false): Maybe<RouterContextPayload>;
export function useRouter(required?: boolean): RouterContextPayload | Maybe<RouterContextPayload> {
    const context = useContext(RouterContext);

    if (required && !context) {
        throw new Error('Router context is missing in React tree');
    }

    return context;
}

export type ContentRefinementValues = {
    hasPermissionToRefineContent: boolean;
    source: ContentRefinementSourceType;
};

type ContextValues = {
    refineContent: ContentRefinementValues;
};

type ContextStates = {
    isContentRefinementGloballyDisabled: boolean;
};

type ContextActions = {
    setIsContentRefinementGloballyDisabled: React.Dispatch<React.SetStateAction<boolean>>;
};

type GenericStateOnPageContextValues = {
    values: ContextValues;
    states: ContextStates;
    actions: ContextActions;
};

const GenericStateOnPageContext = createContext<GenericStateOnPageContextValues | null>(null);

export const useGenericStateOnPageContext = () => {
    const context = useContext(GenericStateOnPageContext);

    return context;
};

export const RouterRoutesContext = createContext<RouteDefinition[]>([]);
export const useRouterRoutes = () => useContext(RouterRoutesContext);
