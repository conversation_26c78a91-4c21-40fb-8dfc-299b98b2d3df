import { SelectProps } from 'antd';
import { useThemeComponents } from '../../themes/hooks';
import useSystemOptions from '../../utilities/useSystemOptions';
import { SelectFieldProps } from './SelectField';

type SalesOfferAgreementFieldProps = Omit<SelectFieldProps, 'children' | 'options'>;

const filterOption: SelectProps['filterOption'] = (inputValue, option) =>
    option.label.toString().toLowerCase().includes(inputValue.toLowerCase());

const SalesOfferAgreementField = (props: SalesOfferAgreementFieldProps) => {
    const {
        FormFields: { SelectField },
    } = useThemeComponents();

    const { salesOfferAgreementKinds } = useSystemOptions();

    return <SelectField {...props} filterOption={filterOption} options={salesOfferAgreementKinds} />;
};

export default SalesOfferAgreementField;
