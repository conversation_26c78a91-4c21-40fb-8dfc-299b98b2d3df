import { ApolloClient, NormalizedCacheObject, useApolloClient } from '@apollo/client';
import { PHeading, PText } from '@porsche-design-system/components-react';
import { Typography, Card as AntdCard } from 'antd';
import { useFormikContext } from 'formik';
import { omit } from 'lodash/fp';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import styled, { css } from 'styled-components';
import { LocalCustomerFieldDataFragment } from '../../api/fragments/LocalCustomerFieldData';
import { TradeInVehicleDataFragment } from '../../api/fragments/TradeInVehicleData';
import {
    GetDataFromMyInfoDocument,
    GetDataFromMyInfoQuery,
    GetDataFromMyInfoQueryVariables,
} from '../../api/queries/getDataFromMyInfo';
import { CustomerKind, LocalCustomerFieldKey, LayoutType } from '../../api/types';
import { ApplicantFormValues, Title } from '../../pages/portal/EventApplicationEntrypoint/ApplicantForm/shared';
import { useThemeComponents } from '../../themes/hooks';
import { Title as TitleOptions } from '../../utilities/useSystemOptions';
import { useRouter } from '../contexts/shared';
import MyInfoIcon from './myInfo.svg';
import SingpassIcon from './singpass.svg';

const Card = styled(AntdCard)`
    border-radius: var(--card-border-radius, 2px);
    background-color: #eff0f1;
    & .ant-card-body {
        padding: 24px;
    }

    ${props =>
        props.theme.layoutType === LayoutType.PorscheV3 &&
        css`
            & .ant-card-body {
                padding: 0;
            }
        `}
`;

const Container = styled.div`
    font-size: 16px;
`;

const MyInfoButton = styled.a`
    cursor: pointer;
`;

const SingpassDescription = styled(Typography.Paragraph)`
    &.ant-typography {
        margin-top: 1rem;
        margin-bottom: 1.5rem;
    }
`;

export type GetMyInfoDataResult = {
    customerFields: LocalCustomerFieldDataFragment[] | null;
    tradeInVehicle?: TradeInVehicleDataFragment[] | null;
};

const getMyInfoData = async (
    linkId: string,
    code: string,
    applicationId: string,
    customerKind: CustomerKind,
    client: ApolloClient<NormalizedCacheObject>,
    withTradeIn?: boolean,
    withTestDrive?: boolean
): Promise<GetMyInfoDataResult> => {
    const { data } = await client.query<GetDataFromMyInfoQuery, GetDataFromMyInfoQueryVariables>({
        query: GetDataFromMyInfoDocument,
        variables: { code, linkId, applicationId, customerKind, withTradeIn, withTestDrive },
        fetchPolicy: 'no-cache',
    });

    if (data?.myInfoData) {
        return data.myInfoData;
    }

    return null;
};

export type MyInfoProps = {
    applicationId: string;
    customerKind: CustomerKind;
    customerFieldPrefix?: string;
    tradeInVehicle?: {
        name: string;
        withTradeIn: boolean;
        withTestDrive: boolean;
    };
    onClick: () => void;
    setWithMyInfo: (value: boolean) => void;
    onLoading?: (isLoading: boolean) => void;
};

type LocationState = { myInfoAuthorizationCode?: string; linkId?: string };

const MyInfo = ({
    applicationId,
    customerKind,
    customerFieldPrefix,
    tradeInVehicle,
    onClick,
    setWithMyInfo,
    onLoading,
}: MyInfoProps) => {
    const { t } = useTranslation('myInfo');
    const { notification, ListItem } = useThemeComponents();
    const client = useApolloClient() as ApolloClient<NormalizedCacheObject>;
    const { setFieldValue, setValues } = useFormikContext();
    const state = useLocation().state as LocationState;
    const { layout } = useRouter();
    const navigate = useNavigate();

    useEffect(() => {
        // Hide myInfo section after loaded data from MyInfo
        if (!state?.myInfoAuthorizationCode && state?.linkId) {
            setWithMyInfo(true);
        }

        if (state?.myInfoAuthorizationCode && state?.linkId) {
            const { myInfoAuthorizationCode, linkId } = state;

            notification.loading({
                content: t('myInfo:messages.loadingData'),
                key: 'primary',
                duration: 0,
            });
            if (onLoading) {
                onLoading(true);
            }

            getMyInfoData(
                linkId,
                myInfoAuthorizationCode,
                applicationId,
                customerKind,
                client,
                tradeInVehicle?.withTradeIn,
                tradeInVehicle?.withTestDrive
            )
                .then(myInfoData => {
                    if (myInfoData?.customerFields && myInfoData.customerFields.length > 0) {
                        const customerValues = {};

                        myInfoData.customerFields.forEach(field => {
                            switch (field.__typename) {
                                case 'LocalCustomerStringField':
                                    if (
                                        field.key === LocalCustomerFieldKey.Title ||
                                        field.key === LocalCustomerFieldKey.NonBinaryTitle
                                    ) {
                                        customerValues[field.key] = {
                                            value: TitleOptions[field.stringValue],
                                            source: field.source,
                                        };
                                    } else {
                                        customerValues[field.key] = { value: field.stringValue, source: field.source };
                                    }

                                    break;

                                case 'LocalCustomerDateField':
                                    customerValues[field.key] = {
                                        value: new Date(field.dateValue),
                                        source: field.source,
                                    };
                                    break;

                                case 'LocalCustomerPhoneField':
                                    customerValues[field.key] = { value: field.phoneValue, source: field.source };
                                    break;

                                case 'LocalCustomerDrivingLicenseField':
                                    customerValues[field.key] = {
                                        value: field.drivingLicenseValue,
                                        source: field.source,
                                    };
                                    break;

                                case 'LocalCustomerNumberField':
                                    customerValues[field.key] = { value: field.numberValue, source: field.source };
                                    break;

                                default:
                                    throw new Error('Local customer field in myinfo not implemented');
                            }
                        });

                        setWithMyInfo(true);
                        // myinfo is only retrieved once time
                        // so need to remove myinfo code to avoid re-load and hide myinfo section
                        const { myInfoAuthorizationCode, ...restState } = state;
                        navigate('.', { replace: true, state: restState });

                        const withTradeIn = tradeInVehicle?.name && myInfoData?.tradeInVehicle?.length;

                        if (customerFieldPrefix) {
                            setValues((prevValues: ApplicantFormValues) => {
                                const previousValues = withTradeIn
                                    ? omit([tradeInVehicle.name], prevValues)
                                    : prevValues;

                                const updatedValues = {
                                    ...previousValues,
                                    customer: {
                                        fields: {
                                            ...prevValues.customer.fields,
                                            ...customerValues,
                                        },
                                    },
                                };

                                if (withTradeIn) {
                                    updatedValues[tradeInVehicle.name] = myInfoData.tradeInVehicle;
                                }

                                return updatedValues;
                            });

                            return;
                        }

                        if (withTradeIn) {
                            setFieldValue(tradeInVehicle.name, myInfoData.tradeInVehicle);
                        }

                        setValues(customerValues);
                    } else {
                        notification.error({
                            content: t('myInfo:messages.error'),
                            key: 'error',
                            duration: 2,
                        });
                    }
                })
                .finally(() => {
                    notification.destroy('primary');
                    if (onLoading) {
                        onLoading(false);
                    }
                });
        }
    }, [
        tradeInVehicle,
        applicationId,
        client,
        customerFieldPrefix,
        customerKind,
        state,
        setFieldValue,
        setValues,
        setWithMyInfo,
        onLoading,
        t,
        notification,
        navigate,
    ]);

    const v3LayoutType = layout?.__typename === 'PorscheV3Layout';

    return (
        <Container className={v3LayoutType ? 'v3-layout-card' : ''}>
            {v3LayoutType ? (
                <>
                    <PHeading size="medium" style={{ marginBottom: '23px' }}>
                        {t('myInfo:title')}
                    </PHeading>
                    <PText style={{ marginBottom: '24px' }}>{t('myInfo:description')}</PText>
                    <PText>
                        <ListItem translationKey="myInfo:list" />
                    </PText>
                </>
            ) : (
                <>
                    <Title>{t('myInfo:title')}</Title>
                    <Typography.Paragraph>{t('myInfo:description')}</Typography.Paragraph>
                    <Typography.Paragraph>
                        <ListItem translationKey="myInfo:list" />
                    </Typography.Paragraph>
                </>
            )}
            <Card
                bordered={false}
                className="v3-layout-card"
                style={v3LayoutType ? { backgroundColor: 'rgba(238, 239, 242, 1)' } : {}}
            >
                <SingpassIcon />
                <SingpassDescription>{t('myInfo:singpassDescription')}</SingpassDescription>
                <MyInfoButton onClick={onClick}>
                    <MyInfoIcon />
                </MyInfoButton>
            </Card>
        </Container>
    );
};

export default MyInfo;
