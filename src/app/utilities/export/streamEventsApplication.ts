/**
 * Stream export event applications excel for large exports
 *
 * This is used in event details > applications list tab for large exports
 */

// eslint-disable-next-line no-console
import { nanoid } from 'nanoid';
import type { LeadStageOption, ApplicationStage, PeriodPayload } from '../../api/types';
import { generateApplicationFilename } from './shared';
import type { CapExcelPurpose, ExportFormat } from './types';

const debug = (message: string, data?: unknown) => {
    console.log(`[EventApplicationExport] ${message}`, data ? JSON.stringify(data, null, 2) : '');
};

export type ExportEventsType = {
    eventId?: string;
    dealerIds: string[];
    company?: { countryCode?: string; displayName?: string };
    token: string | null;
    identifier?: string;
    eventDisplayName?: string;
};

type ExportEventApplicationType = ExportEventsType & {
    applicationIds: string[];
    stage: ApplicationStage;
    period?: PeriodPayload;
    format: ExportFormat;
    capPurpose?: string[];
};

type ExportEventLeadType = ExportEventsType & {
    leadIds: string[];
    stage: LeadStageOption;
    format: ExportFormat;
    period?: PeriodPayload;
};

export const streamExportEventsLead = async ({
    eventId,
    leadIds,
    dealerIds,
    company,
    token,
    identifier,
    eventDisplayName,
    format,
    stage,
    period,
}: ExportEventLeadType) => {
    const nonce = nanoid();

    const headersObj = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };

    const body: Pick<ExportEventLeadType, 'dealerIds' | 'format' | 'stage' | 'leadIds' | 'company' | 'period'> & {
        nonce: string;
    } = { nonce, dealerIds, format, stage, leadIds, company, period };

    const runExport = async (capPurpose?: CapExcelPurpose[], filePurpose?: string[]): Promise<Response> => {
        const endpoint = `/api/export/eventLeads/stream/${eventId}`;

        const generatedFilenames: string[] = [];

        if (Array.isArray(filePurpose) && filePurpose.length > 0) {
            filePurpose.forEach(purpose => {
                generatedFilenames.push(
                    generateApplicationFilename([company?.displayName, eventDisplayName, stage, purpose])
                );
            });
        } else if (format === 'reporting') {
            generatedFilenames.push(generateApplicationFilename([eventDisplayName, stage, 'Report']));
        } else {
            generatedFilenames.push(generateApplicationFilename([company?.displayName, identifier, stage]));
        }

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: headersObj,
            body: JSON.stringify({
                ...body,
                ...(capPurpose && { capPurpose }),
                filename: generatedFilenames,
            }),
        });

        return response;
    };

    const response =
        format === 'cap'
            ? await runExport(['BP_UPLOAD', 'BP_LEAD_UPLOAD'], ['BP', 'BP_LEAD'])
            : await runExport([], []);

    return response;
};

export const streamExportEventsApplication = async ({
    eventId,
    applicationIds,
    dealerIds,
    company,
    token,
    identifier,
    eventDisplayName,
    stage,
    period,
    format,
    capPurpose,
}: ExportEventApplicationType) => {
    debug('Starting event application export with params:', {
        eventId,
        applicationIds: applicationIds.length,
        dealerIds,
        stage,
        period,
        format,
        capPurpose,
    });

    const nonce = nanoid();
    debug('Generated nonce:', nonce);

    const headersObj = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };

    const body: Pick<
        ExportEventApplicationType,
        'dealerIds' | 'stage' | 'applicationIds' | 'company' | 'period' | 'format'
    > & {
        nonce: string;
    } = { nonce, dealerIds, stage, applicationIds, company, period, format };

    const runExport = async (): Promise<Response> => {
        const endpoint = `/api/export/eventApplications/stream/${eventId}`;
        debug(`Preparing export request to ${endpoint}`);

        const filename: string[] = [];
        if (format === 'reporting') {
            filename.push(generateApplicationFilename([eventDisplayName, stage, 'Report']));
        } else {
            filename.push(generateApplicationFilename([company?.displayName, identifier, stage]));
        }

        debug('Making request with params:', {
            method: 'POST',
            bodyLength: JSON.stringify(body).length,
            hasToken: !!token,
            filename,
        });

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: headersObj,
            body: JSON.stringify({
                ...body,
                filename,
            }),
        });

        debug('Received response:', {
            status: response.status,
            ok: response.ok,
            statusText: response.statusText,
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => 'Failed to get error text');
            debug('Export request failed:', {
                status: response.status,
                statusText: response.statusText,
                errorText,
            });
        }

        return response;
    };

    const response = await runExport();

    return response;
};
