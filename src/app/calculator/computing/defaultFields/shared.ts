import dayjs from 'dayjs';
import {
    AffinAutoFinanceCentre,
    ApplicationMarket,
    InsuranceProductType,
    PaymentMode,
    AmountUnit,
} from '../../../api/types';

export type DealerOption = {
    amount: number;
    description?: string;
};

export type CompoundValue = {
    percentage: number;
    amount: number;
};

export type Discount = CompoundValue & {
    description?: string;
    type: AmountUnit;
};

export type BaseCalculatorValues = {
    // base fields
    vehicle: string;
    bank?: string;
    financeProduct?: string;
    term?: number;
    carPrice: number;
    totalPrice: number;
    financedAmount?: number;
    downPayment?: CompoundValue;
    loan?: CompoundValue;
    interestRate?: number;
    deposit?: CompoundValue;
    monthlyInstalments?: { amount: number; start: number; end: number }[];
    paymentMode?: PaymentMode;
    extraAmount?: number;
    extraFinancedAmount?: number;
    promoCodeInput?: string;
    totalInterestPayable?: number;
    // UCCL
    licensePlateFee?: number;
    commission?: number;
    monthlyPaymentFixedInterestRate?: number;
    displacement?: number;
    licenseAndFuelTax?: number;
    insuranceFee?: CompoundValue;
    taxLoss?: number;

    // Trade in (currently in porsche finder)
    tradeInAmount?: number;
    cashAfterTradeIn?: number;

    // HP with Balloon
    balloonPayment?: CompoundValue;

    // HP with Balloon GFV
    assuredResaleValue?: CompoundValue;
    estimatedSurplusBalloon?: number;

    /**
     * Insurance
     */
    insuranceProduct?: string;
    insurerId?: string;
    dateOfBirth?: dayjs.Dayjs;
    yearsOfDriving?: number;
    noClaimDiscount?: number;
    dateOfRegistration?: dayjs.Dayjs;
    insurancePremium?: number | null;
    sumInsured?: number;
    insuranceProductType?: InsuranceProductType;

    mileage?: number;
    residualValue?: CompoundValue;

    dealerOptions: DealerOption[];
    discount?: Discount;

    // When includeDealerOptionsForFinancing is false
    // this value will be shown as down payment, while hiding the downpayment field
    downPaymentWithAdditional?: CompoundValue;

    // these values need to be stored inside the calculator
    // to determine validation for insurance fields
    // these fields should not be saved in server
    isInsuranceEnabled?: boolean;
    isFinancingEnabled?: boolean;

    carPriceAfterDiscount: number;

    affinAutoFinanceCentre?: AffinAutoFinanceCentre;
};

export type DefaultCalculatorValues = BaseCalculatorValues & {
    // market identifier
    market: ApplicationMarket.Default;
};
