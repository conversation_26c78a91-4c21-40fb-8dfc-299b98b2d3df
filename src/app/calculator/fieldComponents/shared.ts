import { TextProps } from 'antd/lib/typography/Text';
import { FC, ReactNode } from 'react';
import { EnhancedFormItemProps } from '../GridCalculatorComparison/fields/shared/FormItem';

export type SummaryFieldLayoutProps = {
    FormItem: FC<EnhancedFormItemProps & { onClick?: () => void }>;
    Text: FC<TextProps & { size?: string; weight?: string; align?: string }>;
    additionalHeight?: number;
    hideLabel?: boolean;
    onClick?: () => void;
    value: ReactNode | string;
    label: ReactNode | string;
    isMultiple?: boolean;
    fieldType?: string;
    disabled?: boolean;
};
